// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON><PERSON>",
    "AUTOSAR",
    "<PERSON>L<PERSON>",
    "chardet",
    "CICD",
    "combobox",
    "DIF<PERSON>",
    "dspinbox",
    "dtags",
    "Eigen",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "gtest",
    "hhmmss",
    "hicpp",
    "Liang",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "MSOP",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "opencv",
    "OPENMP",
    "pcap",
    "Pixmap",
    "QMESSAGE",
    "qobject",
    "qsetting",
    "qsettings",
    "robosense",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "Shen",
    "SHIYAN",
    "spdlog",
    "suteng",
    "tablewidget",
    "tabwidget",
    "utest",
    "widgetaction",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "udev",
    "Thorlabs",
    "lsusb",
    "libusb",
    "usbtmc",
    "TLPM",
    "SCPI",
    "TERMCHAR",
    "TLvisa",
    "RELWITHDEBINFO"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
