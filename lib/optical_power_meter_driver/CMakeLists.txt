﻿cmake_minimum_required(VERSION 3.10.0)
cmake_policy(SET CMP0048 NEW)
cmake_policy(SET CMP0076 NEW)

if(WIN32)
  if(POLICY CMP0074)
    cmake_policy(SET CMP0074 NEW)
  endif(POLICY CMP0074)
endif(WIN32)

if(WIN32)
  set(LIBRARY_OUTPUT_PATH ${CMAKE_BINARY_DIR})
  set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR})
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
  set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
  set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
endif(WIN32)

string(TIMESTAMP PROJECT_COMPILE_DATE "%Y%m%d")
project(optical_power_meter_driver VERSION 2.1.0.${PROJECT_COMPILE_DATE})

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
  set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
endif()

set(CMAKE_BUILD_TYPE RelWithDebInfo)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# =========================
# Option
# =========================
option(BUILD_POWER_METER_EXAMPLE "build example or not" OFF)
option(BUILD_POWER_METER_TEST "build gtest or not" OFF)

message(STATUS "build mems power meter example or not: " ${BUILD_POWER_METER_EXAMPLE})
message(STATUS "build mems power meter test or not: " ${BUILD_POWER_METER_TEST})

find_package(Git QUIET)
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

# =========================
# Get System Name
# =========================
if(CMAKE_SYSTEM_NAME MATCHES "Windows")
  set(LSB_CODENAME win64)
elseif(CMAKE_SYSTEM_NAME MATCHES "Linux")
  find_program(LSB_EXEC lsb_release)

  if(LSB_EXEC MATCHES "NOTFOUND")
    message("\n lsb_release not found, please install using: \n\t sudo apt install lsb_release\n")
  endif()

  execute_process(
    COMMAND ${LSB_EXEC} -cs
    OUTPUT_VARIABLE LSB_CODENAME
    OUTPUT_STRIP_TRAILING_WHITESPACE)
else()
  message(FATAL_ERROR "unsupported system")
endif()

set(RSFSCLOG_TAG "develop")
include(cmake/FindRSFSCLog.cmake)

set(CPP_SOURCE)
file(GLOB TMP_CPP "src/*.cpp")
list(APPEND CPP_SOURCE ${TMP_CPP})

if(CMAKE_SYSTEM_NAME MATCHES "Linux")
  file(GLOB TMP_CPP "src/linux/*.cpp")
  list(APPEND CPP_SOURCE ${TMP_CPP})
endif(CMAKE_SYSTEM_NAME MATCHES "Linux")

message(STATUS ${CPP_SOURCE})
add_library(${PROJECT_NAME} STATIC ${CPP_SOURCE})

if(CMAKE_SYSTEM_NAME MATCHES "Windows")
  target_compile_options(${PROJECT_NAME} PRIVATE /O2 /utf-8)
  target_link_directories(${PROJECT_NAME} PUBLIC lib/opm/${LSB_CODENAME}/lib_x64)
  target_link_libraries(${PROJECT_NAME} PUBLIC TLPM_64.lib)
elseif(CMAKE_SYSTEM_NAME MATCHES "Linux")
  target_compile_options(${PROJECT_NAME} PRIVATE -fPIC -Wall -O3 -g1)
  target_link_directories(${PROJECT_NAME} PUBLIC lib/opm/${LSB_CODENAME}/bin)
  target_link_libraries(${PROJECT_NAME} PUBLIC libTLPM_64.so libusb-1.0.so.0.1.0)
  # 使用TLvisa.h需要
  target_link_libraries(${PROJECT_NAME} PUBLIC dl)
  target_compile_definitions(${PROJECT_NAME} PUBLIC __TL_COMPILE_64BIT__)
  target_include_directories(${PROJECT_NAME} PRIVATE src/linux)
else()
  message(FATAL_ERROR "unsupported system")
endif()

target_include_directories(${PROJECT_NAME} PUBLIC include)
target_include_directories(${PROJECT_NAME} PRIVATE src)
target_include_directories(${PROJECT_NAME} SYSTEM PUBLIC lib/opm/${LSB_CODENAME}/include)
target_link_directories(${PROJECT_NAME} PUBLIC lib/opm/${LSB_CODENAME})

set_target_properties(
  ${PROJECT_NAME}
  PROPERTIES CXX_STANDARD 17
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)

if(BUILD_POWER_METER_EXAMPLE)
  add_subdirectory(example)
endif(BUILD_POWER_METER_EXAMPLE)

# =========================
# Create Unit Test
# =========================
if(BUILD_POWER_METER_TEST)
  add_subdirectory(test)
endif(BUILD_POWER_METER_TEST)

# =========================
# install
# =========================
if(${CMAKE_PROJECT_NAME} EQUAL ${PROJECT_NAME})
  set(INSTALL_SUB_DIR
      ${PROJECT_NAME}
      CACHE STRING "${PROJECT_NAME} or ${CMAKE_PROJECT_NAME}")
else()
  set(INSTALL_SUB_DIR
      ${CMAKE_PROJECT_NAME}
      CACHE STRING "${PROJECT_NAME} or ${CMAKE_PROJECT_NAME}")
endif()

install(
  DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib/opm/${LSB_CODENAME}/bin/
  DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${INSTALL_SUB_DIR}
  USE_SOURCE_PERMISSIONS
  DIRECTORY_PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ)
