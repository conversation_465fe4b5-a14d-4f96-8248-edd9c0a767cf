language: cpp

matrix:
  include:
    - os: osx
      osx_image: xcode11.3
      compiler: clang
      env: Qt5_DIR=/usr/local/opt/qt5/lib/cmake/Qt5

    - os: linux
      dist: xenial
      sudo: false
      compiler: clang
      env: CXX=clang++-7 CC=clang-7 QT=512
      addons:
        apt:
          sources:
            - llvm-toolchain-xenial-7
          packages:
            - clang-7

    - os: linux
      dist: xenial
      sudo: false
      compiler: gcc
      env:
        - CXX=g++-7 CC=gcc-7 QT=512
        - CXXFLAGS="-fsanitize=address -fno-omit-frame-pointer"
        - LDFLAGS=-fsanitize=address
           # Too many false positive leaks:
        - ASAN_OPTIONS=detect_leaks=0
      addons:
        apt:
          sources:
            - ubuntu-toolchain-r-test
          packages:
            - g++-7

git:
  depth: 10

before_install:
   - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then brew update    ; fi
   - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then brew install qt; fi
   - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then sudo apt-get update -qq ; fi
   - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then sudo apt-get install build-essential libgl1-mesa-dev ; fi
   - if [[ "$QT" == "512" ]]; then sudo add-apt-repository ppa:beineri/opt-qt-5.12.1-xenial -y; fi
   - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then sudo apt-get update -qq; fi
   - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then sudo apt-get -yqq install qt${QT}base; source /opt/qt${QT}/bin/qt${QT}-env.sh; fi

script:
  - mkdir build
  - cd build
  - cmake -DCMAKE_VERBOSE_MAKEFILE=$VERBOSE_BUILD .. && make -j
  - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then xvfb-run --server-args="-screen 0 1024x768x24" ctest --output-on-failure; fi
  - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then ctest --output-on-failure; fi

notifications:
  email: false
