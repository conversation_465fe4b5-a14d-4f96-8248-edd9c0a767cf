Development Progress
====================


- [✅ done] Save/restore to Json. Maybe inherit the GraphModel from Serializable
- [✅ done] Vertical layout
- [✅ done] Dynamic ports
- [✅ done] ``AbstractNodeGeometry``, ``AbstractNodePainter``
- [✅ done] Website with documentation
- [➡️  work in progress] Unit-Tests
- [➡️  work in progress] Ctrl+D for copying and inserting a selection duplicate
- [⏸ not started] Node groups
- [⏸ not started] ``ConnectionPaintDelegate``
- [⏸ not started] Check how styles work and what needs to be done. See old pull-requests
- [☝ help needed] Python bindings. Maybe a wrapper using Shiboken
  - Python examples
- [☝ help needed] QML front-end

