name: ros2

on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  industrial_ci:
    strategy:
      matrix:
        env:
          - {ROS_DISTRO: humble, ROS_REPO: main}
          - {ROS_DISTRO: iron, ROS_REPO: main}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: 'ros-industrial/industrial_ci@master'
        env: ${{matrix.env}}
        with:
            package-name: plotjuggler
