name: <PERSON><PERSON> (conda)

on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  pixi_conda_build:
    strategy:
      matrix:
        os:
          - windows-latest
          - ubuntu-latest
    runs-on: ${{ matrix.os }}
    steps:
      # Pixi is the tool used to create/manage conda environment
      - uses: actions/checkout@v3
      - uses: prefix-dev/setup-pixi@v0.8.1
        with:
          pixi-version: v0.40.3
      - name: Build
        run: pixi run build
      - name: Run tests
        run: pixi run test
