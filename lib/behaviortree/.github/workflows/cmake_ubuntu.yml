name: cmake Ubuntu

on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  build:
    # The CMake configure and build commands are platform agnostic and should work equally
    # well on Windows or Mac.  You can convert this to a matrix build if you need
    # cross-platform coverage.
    # See: https://docs.github.com/en/free-pro-team@latest/actions/learn-github-actions/managing-complex-workflows#using-a-build-matrix
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-22.04]

    steps:
    - uses: actions/checkout@v2

    - name: Install Conan
      id: conan
      uses: turtlebrowser/get-conan@main

    - name: Create default profile
      run: conan profile detect

    - name: Create Build Environment
      # Some projects don't allow in-source building, so create a separate build directory
      # We'll use this as our working directory for all subsequent commands
      run: cmake -E make_directory ${{github.workspace}}/build

    - name: Install conan dependencies
      working-directory: ${{github.workspace}}/build
      run: conan install ${{github.workspace}}/conanfile.txt -s build_type=${{env.BUILD_TYPE}} --build=missing

    - name: Configure CMake
      shell: bash
      working-directory: ${{github.workspace}}/build
      run: cmake ${{github.workspace}} -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake

    - name: Build
      shell: bash
      working-directory: ${{github.workspace}}/build
      run: cmake --build . --config ${{env.BUILD_TYPE}}

    - name: run test (Linux)
      working-directory: ${{github.workspace}}/build/tests
      run: ctest

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
