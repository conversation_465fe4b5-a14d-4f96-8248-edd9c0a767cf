﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mems_tcp.h"
#include <array>

#include "rsfsc_log/rsfsc_log.h"

#include <boost/algorithm/hex.hpp>
#include <boost/asio/io_service.hpp>
#include <boost/asio/ip/tcp.hpp>
#include <boost/exception/all.hpp>
#include <cstddef>
#include <cstdint>
#include <fstream>
#include <memory>

#include <mutex>
#include <string>
#include <vector>

namespace robosense
{
namespace lidar
{

constexpr uint32_t FRAME_FLAG               = 0x12345678;
constexpr uint32_t VIEWING_FIELD_PARAM_FLAG = 0x11223344;

constexpr uint32_t NET_CMD_BEGIN                = 0x000;
constexpr uint32_t NET_CMD_READ_REGISTER        = 0x001;
constexpr uint32_t NET_CMD_WRITE_REGISTER       = 0x002;
constexpr uint32_t NET_CMD_READ_FLASH           = 0x003;
constexpr uint32_t NET_CMD_WRITE_FLASH          = 0x004;
constexpr uint32_t NET_CMD_READ_PARAMETER       = 0x005;  // 读网络信息
constexpr uint32_t NET_CMD_WRITE_PARAMETER      = 0x006;
constexpr uint32_t NET_CMD_FPGA_TO_FLASH        = 0x007;  // 固化
constexpr uint32_t NET_CMD_READ_FPGA_MEM        = 0x008;
constexpr uint32_t NET_CMD_WRITE_FPGA_MEM       = 0x009;
constexpr uint32_t NET_CMD_REG_ERASE_FLASH      = 0x00A;
constexpr uint32_t NET_CMD_SAMPLE_STORE         = 0x00B;
constexpr uint32_t NET_CMD_SAMPLE_SEND          = 0x00C;
constexpr uint32_t NET_CMD_SAMPLE_MODE          = 0x00D;
constexpr uint32_t NET_CMD_NORMAL_MODE          = 0x00E;
constexpr uint32_t NET_CMD_TCP_START            = 0x011;
constexpr uint32_t NET_CMD_TCP_END              = 0x012;
constexpr uint32_t NET_CMD_GET_INTENSITY        = 0x013;
constexpr uint32_t NET_CMD_READ_REGISTER_2      = 0x014;
constexpr uint32_t NET_CMD_WRITE_REGISTER_2     = 0x015;
constexpr uint32_t NET_CMD_READ_VIEW_PARAMETER  = 0x016;
constexpr uint32_t NET_CMD_WRITE_VIEW_PARAMETER = 0x017;
constexpr uint32_t NET_CMD_RESET                = 0x018;
constexpr uint32_t NET_CMD_END                  = 0x050;

constexpr uint32_t NET_CMD_ACK_BEGIN                = 0x100;
constexpr uint32_t NET_CMD_ACK_READ_REGISTER        = 0x101;
constexpr uint32_t NET_CMD_ACK_WRITE_REGISTER       = 0x102;
constexpr uint32_t NET_CMD_ACK_READ_FLASH           = 0x103;
constexpr uint32_t NET_CMD_ACK_WRITE_FLASH          = 0x104;
constexpr uint32_t NET_CMD_ACK_READ_PARAMETER       = 0x105;
constexpr uint32_t NET_CMD_ACK_WRITE_PARAMETER      = 0x106;
constexpr uint32_t NET_CMD_ACK_FPGA_TO_FLASH        = 0x107;
constexpr uint32_t NET_CMD_ACK_READ_FPGA_MEM        = 0x108;
constexpr uint32_t NET_CMD_ACK_WRITE_FPGA_MEM       = 0x109;
constexpr uint32_t NET_CMD_ACK_REG_ERASE_FLASH      = 0x10A;
constexpr uint32_t NET_CMD_ACK_SAMPLE_STORE         = 0x10B;
constexpr uint32_t NET_CMD_ACK_SAMPLE_SEND          = 0x10C;
constexpr uint32_t NET_CMD_ACK_SAMPLE_MODE          = 0x10D;
constexpr uint32_t NET_CMD_ACK_NORMAL_MODE          = 0x10E;
constexpr uint32_t NET_CMD_ACK_TCP_START            = 0x111;
constexpr uint32_t NET_CMD_ACK_TCP_END              = 0x112;
constexpr uint32_t NET_CMD_ACK_GET_INTENSITY        = 0x113;
constexpr uint32_t NET_CMD_ACK_READ_REGISTER_2      = 0x114;
constexpr uint32_t NET_CMD_ACK_WRITE_REGISTER_2     = 0x115;
constexpr uint32_t NET_CMD_ACK_READ_VIEW_PARAMETER  = 0x116;
constexpr uint32_t NET_CMD_ACK_WRITE_VIEW_PARAMETER = 0x117;
constexpr uint32_t NET_CMD_ACK_RESET                = 0x118;
constexpr uint32_t NET_CMD_ACK_END                  = 0x150;

using FrameHead = struct
{
  uint32_t frame_flag;
  uint32_t length;
  uint32_t cmd;
  uint32_t check_sum;
};

#pragma pack(1)
using ViewingFieldParam = struct
{
  uint32_t flag;
  struct DataT
  {
    uint8_t symbol;
    uint8_t value[2];
  } data[VIEWING_FIELD_PARAM_SIZE];
};

using ViewingFieldParamMirrork = struct
{
  uint32_t flag;
  struct DataT
  {
    uint8_t symbol;
    uint8_t value[2];
  } data_t[VIEWING_FIELD_PARAM_SIZE];

  uint32_t flag_mirrork;
  struct DataM
  {
    uint8_t symbol;
    uint8_t value[2];
  } data_m[VIEWING_FIELD_PARAM_MIRRORK_SIZE];
};
#pragma pack()

static uint16_t checkSum(const FrameHead& _frame_head)
{
  uint32_t sum = 0;
  sum += _frame_head.frame_flag & 0xFFFFU;
  sum += (_frame_head.frame_flag >> 16U) & 0xFFFFU;

  sum += _frame_head.length & 0xFFFFU;
  sum += (_frame_head.length >> 16U) & 0xFFFFU;

  sum += _frame_head.cmd & 0xFFFFU;
  sum += (_frame_head.cmd >> 16U) & 0xFFFFU;
  sum = (sum >> 16U) + (sum & 0xFFFFU);

  return static_cast<uint16_t>(~sum);
}

inline uint16_t checkSum16(const std::vector<char>& _data_array)
{
  uint32_t sum = 0;
  int i        = 0;
  int length   = _data_array.size();
  while (1 < length)
  {
    sum += (static_cast<uint32_t>((static_cast<uint16_t>(_data_array.at(i)) & 0x00ffU) |
                                  ((static_cast<uint16_t>(_data_array.at(i + 1))) << 8U))) &
           0x0000ffff;
    length -= 2;
    i += 2;
  }

  if (0 != length)
  {
    sum += static_cast<uint32_t>(_data_array.at(i));
  }

  while (0U != (sum >> 16U))
  {
    sum = (sum >> 16U) + (sum & 0xFFFFU);
  }

  return static_cast<uint16_t>(~sum);
}

static std::vector<char> frameHeadPack(const uint32_t _cmd, const uint32_t _length)
{
  std::vector<char> frame_head_array;
  FrameHead frame_head;

  frame_head.frame_flag = FRAME_FLAG;
  frame_head.cmd        = _cmd;
  frame_head.length     = _length;
  frame_head.check_sum  = checkSum(frame_head);
  frame_head_array.insert(frame_head_array.begin(), reinterpret_cast<char*>(&frame_head),
                          reinterpret_cast<char*>(&frame_head) + sizeof(frame_head));

  return frame_head_array;
}

static inline void waitForReach(bool& _is_interrupt, std::size_t _msec_10 = 1)
{
  while (!_is_interrupt)
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    // RSFSCLog::getInstance()->debug("_msec_10: " + std::to_string(_msec_10));
    if (--_msec_10 == 0)
    {
      break;
    }
  }
}

// static bool ping(const std::string& _ip)
// {
//   if (_ip.empty())
//   {
//     return false;
//   }
//   std::string cmd = "ping -c 1 -s 1 " + _ip;
//   int res         = system(cmd.c_str());
//   std::cout << "res :: " << res << std::endl;
//   return res == 0;
// }

std::mutex MEMSTCP::ping_mutex_;

bool MEMSTCP::ping(const std::string& _ip)
{
  if (_ip.empty())
  {
    return false;
  }

  /**
   * @brief '-n 1' = '-c 1': 表示只发送一个ICMP Echo请求
   * '-l 1' = '-s 1': 表示ICMP Echo请求数据包大小为1字节
   * '-w 100' = '-W 0.1': 表示等待 100 毫秒后超时
   * > nul: 将命令的输出重定向到nul，不显示命令的输出
   */
#if defined(_WIN32)
  std::string cmd = "ping " + _ip + " -n 1 -l 1 -w 100 > nul ";
#else
  std::string cmd = "ping -c 1 -s 1 -W 0.1 " + _ip + " > /dev/null 2>&1";
#endif

  // NOTE: system函数本身是非线程安全的函数,这里需要加锁
  std::lock_guard<std::mutex> locker(ping_mutex_);
  int res = system(cmd.c_str());
  return res == 0;
}

MEMSTCP::MEMSTCP()
{
  reg_back_data_.fill(0x0);
  collect_back_data_.fill(0x0);
  read_write_state_.fill(false);
  ptr_io_service_     = std::make_unique<boost::asio::io_service>();
  ptr_deadline_timer_ = std::make_unique<boost::asio::deadline_timer>(*ptr_io_service_);
  ptr_socket_         = std::make_unique<boost::asio::ip::tcp::socket>(*ptr_io_service_);

  ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  checkDeadline();  // Start the persistent actor that checks for deadline expiry.
}

MEMSTCP::~MEMSTCP()
{
  disconnect();
  if (nullptr != ptr_socket_)
  {
    ptr_socket_.reset();
  }

  if (nullptr != ptr_deadline_timer_)
  {
    ptr_deadline_timer_.reset();
  }

  if (nullptr != ptr_io_service_)
  {
    ptr_io_service_.reset();
  }
}

bool MEMSTCP::connect(const std::pair<std::string, uint16_t>& _network_info, const uint32_t _timeout_ms)
{
  return connect(_network_info.first, _network_info.second, _timeout_ms);
}

bool MEMSTCP::connect(const std::string& _ip, const uint16_t _msop_port, const uint32_t _timeout_ms)
{
  //   is_connect_success_ = false;
  ip_   = _ip;
  port_ = _msop_port;

  if (nullptr == ptr_socket_ || nullptr == ptr_deadline_timer_ || nullptr == ptr_io_service_)
  {
    RSFSCLog::getInstance()->error("invalid param, ptr_socket_ or ptr_deadline_timer_ or ptr_io_service_ is null");
    return false;
  }

  bool ping_pass         = false;
  std::size_t ping_times = (_timeout_ms / 100) > 10 ? (_timeout_ms / 100) : 10;
  for (std::size_t i = 0; i < ping_times; ++i)  // NOTE ping lidar will speed up lidar's connect
  {
    if (ping(_ip))
    {
      ping_pass = true;
      break;
    }
  }
  if (!ping_pass)
  {
    RSFSCLog::getInstance()->error(_ip + " is not connectable. Please check it.");
    return false;
  }

  try
  {
    disconnect();
    //async connect
    msg_header_ = "MEMSTCP [" + _ip + ":" + std::to_string(_msop_port) + "] :: ";
    boost::asio::ip::tcp::endpoint ep(boost::asio::ip::address_v4::from_string(_ip), _msop_port);

    boost::system::error_code ec = boost::asio::error::would_block;  // must be set would_block
    ptr_deadline_timer_->expires_from_now(boost::posix_time::milliseconds(_timeout_ms));
    ptr_socket_->async_connect(ep, [&](const boost::system::error_code& _ec) { ec = _ec; });
    do
    {
      ptr_io_service_->run_one();
    } while (boost::asio::error::would_block == ec);

    if (ec)
    {
      RSFSCLog::getInstance()->error(msg_header_ + "connect error, message : " + ec.message());
      return false;
    }

    RSFSCLog::getInstance()->info(msg_header_ + "connect message : " + ec.message());

    //socket setting
    ptr_socket_->set_option(boost::asio::ip::tcp::socket::keep_alive(true));
    ptr_socket_->set_option(boost::asio::ip::tcp::socket::send_buffer_size(1));
    ptr_socket_->set_option(boost::asio::ip::tcp::socket::receive_buffer_size(1));
    // ptr_socket_->set_option(boost::asio::ip::tcp::socket::receive_low_watermark(1));
    // ptr_socket_->set_option(boost::asio::ip::tcp::socket::send_low_watermark(1));
    ptr_socket_->set_option(boost::asio::ip::tcp::no_delay(true));

    flag_thread_run_.store(true);
    ptr_thread_.reset(new std::thread([this]() { slotReadTCPData(); }));

    is_connect_success_ = true;

    writeCMD(NET_CMD_TCP_START, STATE_TCP_START, 20);

    if (!readNetworkInfo())
    {
      return false;
    }

    std::vector<uint32_t> reg_addr_vec { PL_FIRMWARE_VERSION_ADDR, LASER_MODULE_ADDR };
    std::vector<int32_t> read_data;
    if (!readRegData(reg_addr_vec, read_data))
    {
      RSFSCLog::getInstance()->error("MEMSTCP::connect() -> read lidar version info failed");
      return false;
    }

    parseLidarInfo(read_data.at(0), lidar_hardware_serial_, lidar_software_version_, lidar_function_safe_version_);

    checkIsVAVE(read_data.at(1), lidar_hardware_serial_, digital_board_version_);

    if (lidar_hardware_serial_ == LidarHardwareSerial::M2)
    {
      parseLaserModuleAndDigitalBoardInfo(read_data.at(1), laser_module_version_, digital_board_version_);
    }

    return true;
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + " connect failed, reason:" + e.what());
    return false;
  }
}

bool MEMSTCP::disconnect()
{
  std::lock_guard<std::mutex> locker(tcp_mutex_);
  try
  {
    if (isConnected())
    {
      writeCMD(NET_CMD_TCP_END, STATE_TCP_END, 20);
      flag_thread_run_.store(false);
      ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
      boost::system::error_code ec;
      ptr_socket_->shutdown(boost::asio::ip::tcp::socket::shutdown_both, ec);
      if (ec)
      {
        RSFSCLog::getInstance()->error(msg_header_ + std::string("  -> shutdown failed: ") + ec.message());
      }
      ptr_socket_->close(ec);
      if (ec)
      {
        RSFSCLog::getInstance()->error(msg_header_ + std::string("  -> close failed: ") + ec.message());
      }
      if (ptr_thread_ != nullptr && ptr_thread_->joinable())
      {
        RSFSCLog::getInstance()->info(msg_header_ + "stop -> tcp thread joining");
        ptr_thread_->join();
      }
      if (nullptr != ptr_thread_)
      {
        ptr_thread_.reset();
      }
      is_connect_success_ = false;
    }
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error(msg_header_ + std::string("  -> disconnect failed: ") + e.what());
    return false;
  }

  return true;
}

bool MEMSTCP::isConnected() const
{
  if (ptr_socket_ == nullptr)
  {
    RSFSCLog::getInstance()->error("connect error, ptr_socket is null");
    return false;
  }
  return ptr_socket_->is_open() && is_connect_success_;
}

void MEMSTCP::getLidarInfo(LidarHardwareSerial& _hardware_serial,
                           LidarSoftwareVersion& _software_version,
                           LidarFunctionSafeVersion& _func_safe_version)
{
  _hardware_serial   = lidar_hardware_serial_;
  _software_version  = lidar_software_version_;
  _func_safe_version = lidar_function_safe_version_;
}

LaserModuleVersion MEMSTCP::getLaserModuleInfo() { return laser_module_version_; }

DigitalBoardVersion MEMSTCP::getDigitalBoardInfo() { return digital_board_version_; }

void MEMSTCP::abort() { is_abort_.store(true); }
bool MEMSTCP::isAbort() { return is_abort_.load(); };
bool MEMSTCP::reset(const uint32_t _msec_10)
{
  if (writeCMD(NET_CMD_RESET, STATE_RESET, _msec_10))
  {
    return disconnect();
  }
  return false;
}
void MEMSTCP::resetAbortFlag() { is_abort_.store(false); }

void MEMSTCP::slotReadTCPData()
{
  static constexpr std::size_t MAX_READ_BUFFER = 1248;
  std::array<char, MAX_READ_BUFFER> recv_buffer { 0x0 };
  while (flag_thread_run_.load())
  {
    boost::system::error_code ec;
    recv_buffer.fill(0x0);
    std::size_t read_size = ptr_socket_->read_some(boost::asio::buffer(recv_buffer), ec);
    if (ec == boost::asio::error::operation_aborted || read_size == 0)
    {
      RSFSCLog::getInstance()->debug(msg_header_ + "lidar-tcp disconnect first");
      return;
    }
    if (ec && ec != boost::asio::error::eof)
    {
      RSFSCLog::getInstance()->error(std::string(boost::system::system_error(ec).what()));
      continue;
    }

    read_data_buffer_.insert(read_data_buffer_.end(), recv_buffer.begin(), recv_buffer.begin() + read_size);
    while (sizeof(FrameHead) <= static_cast<uint64_t>(read_data_buffer_.size()))
    {
      while (0x78 != read_data_buffer_.at(0) || 0x56 != read_data_buffer_.at(1) || 0x34 != read_data_buffer_.at(2) ||
             0x12 != read_data_buffer_.at(3))
      {
        read_data_buffer_.erase(read_data_buffer_.begin());
        if (sizeof(FrameHead) > static_cast<uint64_t>(read_data_buffer_.size()))
        {
          return;
        }
      }
      FrameHead frame_head;
      char* data = read_data_buffer_.data();
      memcpy(&frame_head, data, sizeof(FrameHead));
      if (frame_head.check_sum != checkSum(frame_head))
      {
        RSFSCLog::getInstance()->warn(msg_header_ + "slotReadTCPData -> check sum error");
        read_data_buffer_.erase(read_data_buffer_.begin(), read_data_buffer_.begin() + 4);
      }
      else
      {
        bool temp_bool   = false;
        int counter_read = 0;
        while (sizeof(FrameHead) + frame_head.length > static_cast<uint64_t>(read_data_buffer_.size()) &&
               counter_read < 10)
        {
          waitForReach(temp_bool, 1);
          recv_buffer.fill(0x0);
          read_size = ptr_socket_->read_some(boost::asio::buffer(recv_buffer), ec);
          if (ec == boost::asio::error::operation_aborted || read_size == 0)
          {
            RSFSCLog::getInstance()->error(msg_header_ + "lidar-tcp disconnect second");
            return;
          }
          if (ec && ec != boost::asio::error::eof)
          {
            RSFSCLog::getInstance()->error(std::string(boost::system::system_error(ec).what()));
            continue;
          }
          read_data_buffer_.insert(read_data_buffer_.end(), recv_buffer.begin(), recv_buffer.begin() + read_size);
          ++counter_read;
        }
        if (counter_read >= 10)
        {
          read_data_buffer_.erase(read_data_buffer_.begin(), read_data_buffer_.begin() + 4);
          RSFSCLog::getInstance()->error("Error in slotReadTCPData has been catch");
          break;
        }

        switch (frame_head.cmd)
        {
        case NET_CMD_ACK_TCP_START:
        {
          read_write_state_[STATE_TCP_START] = true;
          break;
        }
        case NET_CMD_ACK_TCP_END:
        {
          read_write_state_[STATE_TCP_END] = true;
          break;
        }
        case NET_CMD_ACK_READ_REGISTER:  // 读寄存器
        {
          memcpy(reg_back_data_.data(), &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);

          read_write_state_[STATE_READ_REG] = true;
          break;
        }
        case NET_CMD_ACK_READ_REGISTER_2:  // 读连续寄存器, frame_header + reg_addr_begin + number of register +
        {
          uint32_t register_num = 0;
          memcpy(&register_num, &read_data_buffer_.at(sizeof(frame_head) + sizeof(uint32_t)), sizeof(uint32_t));
          if ((register_num + 2) * sizeof(int32_t) == frame_head.length)
          {
            memcpy(reg_back_data_.data(), &read_data_buffer_.at(sizeof(frame_head) + sizeof(uint32_t)),
                   register_num * sizeof(int32_t));
            read_write_state_[STATE_READ_REG_2] = true;
          }
          break;
        }
        case NET_CMD_ACK_WRITE_REGISTER:  // 写寄存器
        {
          read_write_state_[STATE_WRITE_REG] = true;
          break;
        }
        case NET_CMD_ACK_WRITE_REGISTER_2:
        {
          read_write_state_[STATE_WRITE_REG_2] = true;
          break;
        }
        case NET_CMD_ACK_GET_INTENSITY:
        {
          memcpy(collect_back_data_.data(), &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);
          read_write_state_[STATE_READ_INTENSITY] = true;
          break;
        }
        case NET_CMD_ACK_READ_PARAMETER:
        {
          memcpy(&net_work_info_, &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);
          read_write_state_[STATE_READ_NETWORK_INFO] = true;
          break;
        }
        case NET_CMD_ACK_WRITE_PARAMETER:
        {
          read_write_state_[STATE_WRITE_NETWORK_INFO] = true;
          break;
        }
        case NET_CMD_ACK_READ_VIEW_PARAMETER:
        {
          if (frame_head.length == sizeof(ViewingFieldParam))
          {
            memcpy(reg_back_data_.data(), &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);
            reg_back_data_length_                             = frame_head.length;
            read_write_state_[STATE_READ_VIEWING_FIELD_PARAM] = true;
          }
          else if (frame_head.length == sizeof(ViewingFieldParamMirrork))
          {
            memcpy(reg_back_data_.data(), &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);
            reg_back_data_length_                             = frame_head.length;
            read_write_state_[STATE_READ_VIEWING_FIELD_PARAM] = true;
          }
          break;
        }
        case NET_CMD_ACK_WRITE_VIEW_PARAMETER:
        {
          if (frame_head.length == 0)
          {
            read_write_state_[STATE_WRITE_SPLICE_PARAMS] = true;
          }
          else if (frame_head.length == 1)
          {
            uint8_t state = 0xFF;
            memcpy(&state, &read_data_buffer_.at(sizeof(frame_head)), frame_head.length);
            if (0 == state)
            {
              read_write_state_[STATE_WRITE_SPLICE_PARAMS] = true;
            }
            else if (1 == state)
            {
              RSFSCLog::getInstance()->error(msg_header_ +
                                             "writeViewingFieldParams -> Write failed with length error.");
            }
            else if (2 == state)
            {
              RSFSCLog::getInstance()->error(msg_header_ +
                                             "writeViewingFieldParams -> Write failed with flashCheck error.");
            }
          }
          break;
        }
        case NET_CMD_ACK_FPGA_TO_FLASH:
        {
          int32_t state = 0;
          if (frame_head.length > 0)
          {
            memcpy(&state, &read_data_buffer_.at(sizeof(frame_head)), sizeof(state));
          }
          if (0 == state)
          {
            read_write_state_[STATE_FIX_REG] = true;
          }
          break;
        }
        case NET_CMD_ACK_RESET:
        {
          read_write_state_[STATE_RESET] = true;
          RSFSCLog::getInstance()->info(msg_header_ +
                                        "slotReadTCPData -> reset successfully, please connect MEMSTCP again");
          // NOTE: the state of tcp is disconnected after reset ECU, you need to reconnect MEMS.
          //   disconnect();
          break;
        }
        case NET_CMD_ACK_WRITE_FLASH:
        {
          int32_t state = -1;
          memcpy(&state, &read_data_buffer_.at(sizeof(frame_head)), sizeof(state));
          //std::cout << "state:" << state << std::endl;
          if (0 == state)
          {
            read_write_state_[STATE_WRITE_FLASH] = true;
            RSFSCLog::getInstance()->info(msg_header_ + "slotReadTCPData -> update firmware successfully");
          }
          else if (0xF1 == state)
          {
            RSFSCLog::getInstance()->error(msg_header_ + "slotReadTCPData -> FW transport with checksum error");
          }
          else if (0xF2 == state)
          {
            RSFSCLog::getInstance()->error(msg_header_ + "slotReadTCPData -> FW Flash write with checksum error");
          }
          break;
        }
        case NET_CMD_ACK_REG_ERASE_FLASH:
        {
          int32_t state = 0;
          if (frame_head.length > 0)
          {
            memcpy(&state, &read_data_buffer_.at(sizeof(frame_head)), sizeof(state));
            RSFSCLog::getInstance()->debug(msg_header_ +
                                           "slotReadTCPData -> erase register return value: " + std::to_string(state));
          }
          if (0 == state)
          {
            read_write_state_[STATE_ERASE_REGISTER] = true;
          }
          break;
        }
        default: break;
        }
        read_data_buffer_.erase(
          read_data_buffer_.begin(),
          read_data_buffer_.begin() + static_cast<int32_t>(sizeof(frame_head) + frame_head.length));
      }
    }
  }
}

bool MEMSTCP::writeRegData(const std::vector<uint32_t>& _reg_addr,
                           const std::vector<int32_t>& _reg_val,
                           const uint32_t _msec_10,
                           const std::string& _download_data_path)
{
  std::lock_guard<std::mutex> locker(tcp_mutex_);

  uint16_t group  = _reg_addr.size() / MAX_REGISTER_SIZE_PER_TIME;  // 多少组数据
  uint16_t remain = _reg_addr.size() % MAX_REGISTER_SIZE_PER_TIME;  // 余量
  std::vector<uint32_t> reg_addr_temp(MAX_REGISTER_SIZE_PER_TIME, 0);
  std::vector<int32_t> reg_val_temp(MAX_REGISTER_SIZE_PER_TIME, 0);

  for (int i = 0; i < group; i++)
  {
    reg_addr_temp.assign(_reg_addr.begin() + i * MAX_REGISTER_SIZE_PER_TIME,
                         _reg_addr.begin() + (i + 1) * MAX_REGISTER_SIZE_PER_TIME);
    reg_val_temp.assign(_reg_val.begin() + i * MAX_REGISTER_SIZE_PER_TIME,
                        _reg_val.begin() + (i + 1) * MAX_REGISTER_SIZE_PER_TIME);

    if (!writeRegDataWithLimit(reg_addr_temp, reg_val_temp, _msec_10, _download_data_path))
    {
      return false;
    }
  }

  if (remain > 0)
  {
    reg_addr_temp.resize(remain);
    reg_val_temp.resize(remain);
    reg_addr_temp.assign(_reg_addr.begin() + group * MAX_REGISTER_SIZE_PER_TIME,
                         _reg_addr.begin() + group * MAX_REGISTER_SIZE_PER_TIME + remain);
    reg_val_temp.assign(_reg_val.begin() + group * MAX_REGISTER_SIZE_PER_TIME,
                        _reg_val.begin() + group * MAX_REGISTER_SIZE_PER_TIME + remain);

    if (!writeRegDataWithLimit(reg_addr_temp, reg_val_temp, _msec_10, _download_data_path))
    {
      return false;
    }
  }
  return true;
}

bool MEMSTCP::writeRegDataWithLimit(const std::vector<uint32_t>& _reg_addr,
                                    const std::vector<int32_t>& _reg_val,
                                    const uint32_t _msec_10,
                                    const std::string& _download_data_path)
{
  if (_reg_addr.size() != _reg_val.size())
  {
    return false;
  }
  if (is_abort_)
  {
    RSFSCLog::getInstance()->error("writeRegData abort execute");
    return false;
  }
  //writeCMD(NET_CMD_TCP_START, STATE_TCP_START, 1);
  //writeCMD(NET_CMD_TCP_START, STATE_TCP_START, 1);
  std::vector<char> send_data =
    frameHeadPack(NET_CMD_WRITE_REGISTER, (sizeof(uint32_t) + sizeof(int32_t)) * _reg_addr.size());
  for (std::size_t i = 0, reg_addr_size = _reg_addr.size(); i < reg_addr_size; ++i)
  {
    if (_reg_addr[i] > MAX_REG_ADDR || _reg_addr[i] < MIN_REG_ADDR)
    {
      return false;
    }
    send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<uint32_t*>(&_reg_addr[i])),
                     reinterpret_cast<char*>(const_cast<uint32_t*>(&_reg_addr[i])) + sizeof(uint32_t));
    send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<int32_t*>(&_reg_val[i])),
                     reinterpret_cast<char*>(const_cast<int32_t*>(&_reg_val[i])) + sizeof(int32_t));
  }

  if (!_download_data_path.empty())
  {
    writeRegisterDataToFile(_reg_addr, _reg_val, _download_data_path);
  }

  return sendData(send_data, STATE_WRITE_REG, _msec_10) || sendData(send_data, STATE_WRITE_REG, 2 * _msec_10);
}

bool MEMSTCP::writeRegData(const uint32_t _start_reg_addr, std::vector<int32_t>& _reg_val, const uint32_t _msec_10)
{
  std::lock_guard<std::mutex> locker(tcp_mutex_);

  int reg_number = _reg_val.size();
  if (((_start_reg_addr % 4) != 0U) || (_start_reg_addr + sizeof(uint32_t) * reg_number) > MAX_REG_ADDR ||
      _start_reg_addr < MIN_REG_ADDR || reg_number < 1)
  {
    RSFSCLog::getInstance()->error(msg_header_ + "writeRegData -> incorrect reg addr: " +
                                   uintToHexString(_start_reg_addr) + " and number: " + std::to_string(reg_number));
    return false;
  }
  if (is_abort_)
  {
    RSFSCLog::getInstance()->error("writeRegData abort execute");
    return false;
  }
  std::vector<char> send_data = frameHeadPack(NET_CMD_WRITE_REGISTER_2, sizeof(uint32_t) * (reg_number + 2));
  send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<uint32_t*>(&_start_reg_addr)),
                   reinterpret_cast<char*>(const_cast<uint32_t*>(&_start_reg_addr)) + sizeof(uint32_t));
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_number),
                   reinterpret_cast<char*>(&reg_number) + sizeof(uint32_t));
  for (int i = 0; i < reg_number; ++i)
  {
    send_data.insert(send_data.end(), reinterpret_cast<char*>(&_reg_val[i]),
                     reinterpret_cast<char*>(&_reg_val[i]) + sizeof(uint32_t));
  }

  return sendData(send_data, STATE_WRITE_REG_2, _msec_10) || sendData(send_data, STATE_WRITE_REG_2, 2 * _msec_10);
}

bool MEMSTCP::writeRegData(const std::pair<std::vector<uint32_t>, std::vector<int32_t>>& _reg_addr_and_value,
                           const uint32_t _msec_10,
                           const std::string& _download_data_path)
{
  if (_reg_addr_and_value.first.size() != _reg_addr_and_value.second.size())
  {
    RSFSCLog::getInstance()->error("MEMSTCP::writeRegData -> size of _reg_addr_and_value is not correct");
    return false;
  }

  return writeRegData(_reg_addr_and_value.first, _reg_addr_and_value.second, _msec_10, _download_data_path);
}

bool MEMSTCP::readRegData(const std::vector<uint32_t>& _addrs2read,
                          std::vector<int32_t>& _reg_val,
                          const uint32_t _msec_10)
{
  std::lock_guard<std::mutex> locker(tcp_mutex_);

  if (is_abort_)
  {
    RSFSCLog::getInstance()->error("readRegData abort execute");
    return false;
  }

  std::vector<char> send_data = frameHeadPack(NET_CMD_READ_REGISTER, sizeof(uint32_t) * _addrs2read.size());

  for (const unsigned int& it : _addrs2read)
  {
    if (((it % 4) != 0U) || it > MAX_REG_ADDR || it < MIN_REG_ADDR)
    {
      RSFSCLog::getInstance()->error(msg_header_ + "readRegData -> incorrect reg addr: " + uintToHexString(it));
      return false;
    }
    send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<uint32_t*>(&it)),
                     reinterpret_cast<char*>(const_cast<uint32_t*>(&it)) + sizeof(uint32_t));
  }
  //writeCMD(NET_CMD_TCP_START, STATE_TCP_START, 1);
  bool result = false;
  if (sendData(send_data, STATE_READ_REG, _msec_10) || sendData(send_data, STATE_READ_REG, 2 * _msec_10))
  {
    _reg_val.resize(_addrs2read.size());
    for (std::size_t i = 0, addr_size = _addrs2read.size(); i < addr_size; ++i)
    {
      memcpy(&_reg_val.at(i), &reg_back_data_.at(4 * i), sizeof(int32_t));
    }
    result = true;
  }
  else
  {
    result = false;
  }
  //writeCMD(NET_CMD_TCP_END, STATE_TCP_END, 1);
  return result;
}

bool MEMSTCP::readRegData(const uint32_t _start_reg_addr,
                          const uint32_t _reg_number,
                          std::vector<int32_t>& _reg_val,
                          const uint32_t _msec_10)
{
  std::lock_guard<std::mutex> locker(tcp_mutex_);

  if (((_start_reg_addr % 4) != 0U) || _start_reg_addr > MAX_REG_ADDR ||
      (_start_reg_addr + sizeof(uint32_t) * _reg_number) > MAX_REG_ADDR || _start_reg_addr < MIN_REG_ADDR)
  {
    RSFSCLog::getInstance()->error(msg_header_ + "readRegData -> incorrect reg addr: " +
                                   uintToHexString(_start_reg_addr) + " and number: " + std::to_string(_reg_number));
    return false;
  }
  if (is_abort_)
  {
    RSFSCLog::getInstance()->error("readRegData abort execute");
    return false;
  }
  std::vector<char> send_data = frameHeadPack(NET_CMD_READ_REGISTER_2, sizeof(uint32_t) * 2);
  send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<uint32_t*>(&_start_reg_addr)),
                   reinterpret_cast<char*>(const_cast<uint32_t*>(&_start_reg_addr)) + sizeof(uint32_t));
  send_data.insert(send_data.end(), reinterpret_cast<char*>(const_cast<uint32_t*>(&_reg_number)),
                   reinterpret_cast<char*>(const_cast<uint32_t*>(&_reg_number)) + sizeof(uint32_t));

  bool result = false;
  if (sendData(send_data, STATE_READ_REG_2, _msec_10) || sendData(send_data, STATE_READ_REG_2, 2 * _msec_10))
  {
    _reg_val.resize(_reg_number);
    for (std::size_t i = 0; i < _reg_number; ++i)
    {
      memcpy(&_reg_val[i], &reg_back_data_.at(4 * i), sizeof(int32_t));
    }
    result = true;
  }
  else
  {
    result = false;
  }
  //writeCMD(NET_CMD_TCP_END, STATE_TCP_END, 1);
  return result;
}

void MEMSTCP::getNetworkInfo(NetworkInfo& _network_info) const { _network_info = net_work_info_; }

bool MEMSTCP::setNetworkInfo(const NetworkInfo& _network_info, const uint32_t _msec_10)
{
  std::vector<char> send_data = frameHeadPack(NET_CMD_WRITE_PARAMETER, sizeof(_network_info));
  send_data.insert(send_data.end(), reinterpret_cast<const char*>(&_network_info),
                   reinterpret_cast<const char*>(&_network_info) + sizeof(_network_info));
  return sendData(send_data, STATE_WRITE_NETWORK_INFO, _msec_10) ||
         sendData(send_data, STATE_WRITE_NETWORK_INFO, 2 * _msec_10);
}

bool MEMSTCP::readNetworkInfo() { return writeCMD(NET_CMD_READ_PARAMETER, STATE_READ_NETWORK_INFO, 20); }

bool MEMSTCP::readViewingFieldParams(std::vector<double>& _param)
{
  if (isConnected() && writeCMD(NET_CMD_READ_VIEW_PARAMETER, STATE_READ_VIEWING_FIELD_PARAM, 100))
  {
    if (reg_back_data_length_ == sizeof(ViewingFieldParam))
    {
      ViewingFieldParam vfp;
      memcpy(&vfp, reg_back_data_.data(), sizeof(ViewingFieldParam));
      if (vfp.flag == VIEWING_FIELD_PARAM_FLAG)
      {
        std::vector<double>().swap(_param);
        double temp = 0.0;
        for (auto& it : vfp.data)
        {
          temp = static_cast<double>((static_cast<uint16_t>(it.value[0]) << 8U) + static_cast<uint16_t>(it.value[1]));
          temp /= 100.0;
          if (0U != it.symbol)
          {
            temp = 0.0 - temp;
          }
          _param.emplace_back(temp);
        }
        return true;
      }
    }
    else if (reg_back_data_length_ == sizeof(ViewingFieldParamMirrork))
    {
      ViewingFieldParamMirrork vfp;
      memcpy(&vfp, reg_back_data_.data(), sizeof(ViewingFieldParamMirrork));
      if (vfp.flag == VIEWING_FIELD_PARAM_FLAG)
      {
        std::vector<double>().swap(_param);
        double temp = 0.0;
        for (auto& it : vfp.data_t)
        {
          temp = static_cast<double>((static_cast<uint16_t>(it.value[0]) << 8U) + static_cast<uint16_t>(it.value[1]));
          temp /= 100.0;
          if (0U != it.symbol)
          {
            temp = 0.0 - temp;
          }
          _param.push_back(temp);
        }
      }

      double temp = 0.0;
      for (auto& it : vfp.data_m)
      {
        temp = static_cast<double>((static_cast<uint16_t>(it.value[0]) << 8U) + static_cast<uint16_t>(it.value[1]));
        temp /= 65535.0;
        if (0U != it.symbol)
        {
          temp = 0.0 - temp;
        }
        _param.push_back(temp);
      }
      return true;
    }
  }

  return false;
}

bool MEMSTCP::writeViewingFieldParams(const std::string& _csv_path, const std::string& _mirrork_csv_path)
{
  std::ifstream fin(_csv_path, std::ifstream::in | std::ifstream::binary);
  if (!fin.is_open())
  {
    RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) + " -> can not be open");
    return false;
  }

  std::vector<std::string> string_list;

  std::string line;
  while (std::getline(fin, line))
  {
    string_list.emplace_back(line);
  }

  if (string_list.size() < static_cast<int>(VIEWING_FIELD_PARAM_SIZE))
  {
    RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) +
                                   " -> read file data error, read size (" + std::to_string(string_list.size()) +
                                   ") is not equal to target size(" + std::to_string(VIEWING_FIELD_PARAM_SIZE) + ")");
    return false;
  }

  std::vector<double> param;

  for (std::size_t i = 0; i < VIEWING_FIELD_PARAM_SIZE; ++i)
  {
    param.emplace_back(static_cast<double>(std::stod(string_list.at(i))));
  }

  if (_mirrork_csv_path.empty())
  {
    return writeViewingFieldParams(param);
  }
  else
  {
    std::ifstream fin(_mirrork_csv_path, std::ifstream::in | std::ifstream::binary);
    if (!fin.is_open())
    {
      RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) + " -> can not be open");
      return false;
    }

    std::vector<std::string> string_list;

    std::string line;
    while (std::getline(fin, line))
    {
      string_list.emplace_back(line);
    }

    if (string_list.size() < static_cast<int>(VIEWING_FIELD_PARAM_MIRRORK_SIZE))
    {
      RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) +
                                     " -> read file data error, read size (" + std::to_string(string_list.size()) +
                                     ") is not equal to target size(" +
                                     std::to_string(VIEWING_FIELD_PARAM_MIRRORK_SIZE) + ")");
      return false;
    }

    for (std::size_t i = 0; i < VIEWING_FIELD_PARAM_MIRRORK_SIZE; ++i)
    {
      param.emplace_back(static_cast<double>(std::stod(string_list.at(i))));
    }
    return writeViewingFieldParams(param);
  }
}

bool MEMSTCP::writeViewingFieldParams(const std::vector<double>& _param)
{
  if (_param.size() < VIEWING_FIELD_PARAM_SIZE)
  {
    RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) +
                                   " -> param error, _param.size() = " + std::to_string(_param.size()) +
                                   ", expect = " + std::to_string(VIEWING_FIELD_PARAM_SIZE));
    return false;
  }

  //不带mirrork
  if (_param.size() == 20)
  {
    ViewingFieldParam vfp;
    vfp.flag = VIEWING_FIELD_PARAM_FLAG;
    for (std::size_t i = 0; i < VIEWING_FIELD_PARAM_SIZE; ++i)
    {
      uint16_t temp_value = 0;
      if (_param.at(i) < 0.0)
      {
        vfp.data[i].symbol = 1;
        temp_value         = static_cast<uint16_t>(0 - _param.at(i) * 100.0);
      }
      else
      {
        vfp.data[i].symbol = 0;
        temp_value         = static_cast<uint16_t>(_param.at(i) * 100.0);
      }
      vfp.data[i].value[0] = static_cast<uint8_t>(temp_value >> 8U);
      vfp.data[i].value[1] = static_cast<uint8_t>(temp_value);
    }
    std::vector<char> send_data = frameHeadPack(NET_CMD_WRITE_VIEW_PARAMETER, sizeof(ViewingFieldParam));
    send_data.insert(send_data.end(), reinterpret_cast<char*>(&vfp),
                     reinterpret_cast<char*>(&vfp) + sizeof(ViewingFieldParam));
    return sendData(send_data, STATE_WRITE_SPLICE_PARAMS, 100) || sendData(send_data, STATE_WRITE_SPLICE_PARAMS, 200);
  }
  else if (_param.size() == 25)
  {
    ViewingFieldParamMirrork vfp;
    vfp.flag         = VIEWING_FIELD_PARAM_FLAG;
    vfp.flag_mirrork = VIEWING_FIELD_PARAM_FLAG;

    for (std::size_t i = 0; i < VIEWING_FIELD_PARAM_SIZE; ++i)
    {
      uint16_t temp_value = 0;
      if (_param.at(i) < 0.0)
      {
        vfp.data_t[i].symbol = 1;
        temp_value           = static_cast<uint16_t>(0 - _param.at(i) * 100.0);
      }
      else
      {
        vfp.data_t[i].symbol = 0;
        temp_value           = static_cast<uint16_t>(_param.at(i) * 100.0);
      }
      vfp.data_t[i].value[0] = static_cast<uint8_t>(temp_value >> 8U);
      vfp.data_t[i].value[1] = static_cast<uint8_t>(temp_value);
    }

    for (std::size_t i = 0; i < VIEWING_FIELD_PARAM_MIRRORK_SIZE; ++i)
    {
      uint16_t temp_value = 0;
      if (_param.at(20 + i) < 0.0)
      {
        vfp.data_m[i].symbol = 1;
        temp_value           = static_cast<uint16_t>(0 - _param.at(20 + i) * 65535);
      }
      else
      {
        vfp.data_m[i].symbol = 0;
        temp_value           = static_cast<uint16_t>(_param.at(20 + i) * 65535);
      }
      vfp.data_m[i].value[0] = static_cast<uint8_t>(temp_value >> 8U);
      vfp.data_m[i].value[1] = static_cast<uint8_t>(temp_value);
    }

    std::vector<char> send_data = frameHeadPack(NET_CMD_WRITE_VIEW_PARAMETER, sizeof(ViewingFieldParamMirrork));
    send_data.insert(send_data.end(), reinterpret_cast<char*>(&vfp),
                     reinterpret_cast<char*>(&vfp) + sizeof(ViewingFieldParamMirrork));
    return sendData(send_data, STATE_WRITE_SPLICE_PARAMS, 100) || sendData(send_data, STATE_WRITE_SPLICE_PARAMS, 200);
  }

  return false;
}

bool MEMSTCP::fixRegister(const uint32_t _msec_10) { return writeCMD(NET_CMD_FPGA_TO_FLASH, STATE_FIX_REG, _msec_10); }

bool MEMSTCP::getIntensityData(const std::vector<uint32_t>& _addrs2read,
                               std::vector<int32_t>& _calib_data,
                               const uint32_t _msec_10)
{
  uint32_t reg_val  = 0;
  uint32_t reg_addr = 0;
  std::vector<char> send_data =
    frameHeadPack(NET_CMD_GET_INTENSITY, (sizeof(reg_addr) + sizeof(reg_addr)) * (2 + _addrs2read.size()));
  constexpr uint32_t REF_DATA_UPDATE_EN_ADDR = 0x83c00050;  // 当前通道更新使能, 2.0 的为 0x83C200B8
  reg_addr                                   = REF_DATA_UPDATE_EN_ADDR;
  reg_val                                    = static_cast<uint32_t>(0x1);
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_addr),
                   reinterpret_cast<char*>(&reg_addr) + sizeof(reg_addr));
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_val),
                   reinterpret_cast<char*>(&reg_val) + sizeof(reg_val));

  reg_addr = REF_DATA_UPDATE_EN_ADDR;
  reg_val  = static_cast<uint32_t>(0x0);
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_addr),
                   reinterpret_cast<char*>(&reg_addr) + sizeof(reg_addr));
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_val),
                   reinterpret_cast<char*>(&reg_val) + sizeof(reg_val));

  for (unsigned int it : _addrs2read)
  {
    reg_addr = it;
    if (((reg_addr % 4) != 0U) || reg_addr > MAX_REG_ADDR || reg_addr < MIN_REG_ADDR)
    {
      RSFSCLog::getInstance()->error(msg_header_ +
                                     "getIntensityData -> incorrect reg addr: " + uintToHexString(reg_addr));
      return false;
    }
    reg_val = static_cast<uint32_t>(0x1000000);
    send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_addr),
                     reinterpret_cast<char*>(&reg_addr) + sizeof(reg_addr));
    send_data.insert(send_data.end(), reinterpret_cast<char*>(&reg_val),
                     +reinterpret_cast<char*>(&reg_val) + sizeof(reg_val));
  }

  if (!sendData(send_data, STATE_READ_INTENSITY, _msec_10) && !sendData(send_data, STATE_READ_INTENSITY, 2 * _msec_10))
  {
    return false;
  }

  _calib_data.resize(_addrs2read.size());
  for (int i = 0, addr_size = _addrs2read.size(); i < addr_size; ++i)
  {
    memcpy(&_calib_data.at(i), &collect_back_data_.at(8 * (2 + i) + 4), sizeof(int32_t));
  }

  return true;
}

bool MEMSTCP::writeCMD(const uint32_t _cmd, const ReadWriteStateIndex _index, uint32_t _msec_10)
{
  std::vector<char> send_data = frameHeadPack(_cmd, 0);
  return sendData(send_data, _index, _msec_10) || sendData(send_data, _index, 2 * _msec_10);
}

bool MEMSTCP::sendData(const std::vector<char>& _send_data, const ReadWriteStateIndex _index, uint32_t _msec_10)
{

  if (ptr_socket_ == nullptr)
  {
    RSFSCLog::getInstance()->error(std::string(static_cast<const char*>(__FUNCTION__)) +
                                   " -> connect error, ptr_socket is null");
    return false;
  }

  if (!isConnected())
  {
    //TODO: reconnect
    // disconnect();
    // RSFSCLog::getInstance()->debug("reconnect");
    connect(ip_, port_, 2000);
  }

  read_write_state_.at(_index) = false;

  if (isConnected())
  {
    try
    {
      boost::system::error_code ec;
      std::size_t write_size = boost::asio::write(*ptr_socket_, boost::asio::buffer(_send_data), ec);

      if (ec || write_size != _send_data.size())
      {
        RSFSCLog::getInstance()->error("boost write data error ec: {0}", ec.message());
        return false;
      }
    }
    catch (const boost::exception& ex)
    {
      // handle Boost exception
      RSFSCLog::getInstance()->error(
        "MEMSTCP::sendData() -> sendData write data failed with boost::exception, ex msg: " +
        boost::diagnostic_information(ex));
      return false;
    }
    catch (const boost::system::system_error& ex)
    {
      // handle Boost system error
      RSFSCLog::getInstance()->error(
        "MEMSTCP::sendData() -> write data failed with boost::system::system_error, ex msg: " + std::string(ex.what()));
      return false;
    }
    catch (const std::exception& ex)
    {
      // handle standard exception
      RSFSCLog::getInstance()->error("MEMSTCP::sendData() -> write data failed with std::exception, ex msg: " +
                                     std::string(ex.what()));
      return false;
    }
    catch (...)
    {
      // handle unknown exception
      RSFSCLog::getInstance()->error("MEMSTCP::sendData() -> write data failed with unknown exception");
      return false;
    }

    waitForReach(read_write_state_.at(_index), _msec_10);

    if (STATE_TCP_START != _index && STATE_TCP_END != _index && !read_write_state_.at(_index))
    {
      std::vector<char> send_data_cut;
      if (_send_data.size() > 50)
      {
        send_data_cut.assign(_send_data.begin(), _send_data.begin() + 50);
      }
      else
      {
        send_data_cut.assign(_send_data.begin(), _send_data.end());
      }

      std::string hex_data_str;
      boost::algorithm::hex(send_data_cut.begin(), send_data_cut.end(), std::back_inserter(hex_data_str));
      RSFSCLog::getInstance()->error(msg_header_ + "sendData -> failed to send data: " + hex_data_str +
                                     std::string(" of index ") + std::to_string(_index) + std::string(" in time ") +
                                     std::to_string(_msec_10) + std::string(" * 10msec"));
    }
  }
  else
  {
    RSFSCLog::getInstance()->error("MEMSTCP doesn't connect, please check it.");
  }

  return read_write_state_.at(_index);
}

bool MEMSTCP::softResetLidar(const uint32_t _msec_10) { return writeCMD(NET_CMD_RESET, STATE_RESET, _msec_10); }

bool MEMSTCP::writeToFlash(const std::string& _firmware_path, const uint32_t _addr_offset, const uint32_t _msec_10)
{
  bool is_ok = false;

  std::ifstream in_stream(_firmware_path, std::ios::in | std::ios::binary);

  if (!in_stream.is_open())
  {
    RSFSCLog::getInstance()->error(msg_header_ + "writeToFlash -> open file failed, file path: " + _firmware_path);
    return false;
  }

  in_stream.seekg(0, std::ios::end);  //将输入指针指向文件末尾
  auto length = in_stream.tellg();    //获取当前指针位置
  RSFSCLog::getInstance()->info("the length of the file is " + std::to_string(length) + " bytes");

  std::vector<char> data_array(length, 0x0);  //读取文件数组
  //std::vector<char> data;
  in_stream.seekg(0, std::ios::beg);  //将输入指针指向文件开头
  if (!in_stream.eof())
  {
    in_stream.read(data_array.data(), length);  //读入文件流
    RSFSCLog::getInstance()->info(msg_header_ + "writeToFlash -> read file length: " + std::to_string(length));
    in_stream.close();
  }
  else
  {
    in_stream.close();
    return false;
  }

  if (data_array.size() < 0xa00000 && data_array.size() > 0x400000)
  {
    is_ok = writeToFlash(data_array, _addr_offset, _msec_10);
  }
  else
  {
    RSFSCLog::getInstance()->error(msg_header_ +
                                   "writeToFlash -> file data length error: " + std::to_string(data_array.size()));
  }

  return is_ok;
}

bool MEMSTCP::writeToFlash(const std::vector<char>& _data, const uint32_t _addr_offset, const uint32_t _msec_10)
{
  bool is_ok = false;

  std::vector<char> send_data = frameHeadPack(NET_CMD_WRITE_FLASH, static_cast<uint32_t>(_data.size()) + 12);
  send_data.insert(send_data.end(), reinterpret_cast<const char*>(&_addr_offset),
                   reinterpret_cast<const char*>(&_addr_offset) + sizeof(_addr_offset));
  uint32_t check_sum = static_cast<uint32_t>(checkSum16(_data));
  send_data.insert(send_data.end(), reinterpret_cast<const char*>(&check_sum),
                   reinterpret_cast<const char*>(&check_sum) + sizeof(check_sum));

  uint32_t flash_size = static_cast<uint32_t>(_data.size());
  send_data.insert(send_data.end(), reinterpret_cast<char*>(&flash_size),
                   reinterpret_cast<char*>(&flash_size) + sizeof(flash_size));
  send_data.insert(send_data.end(), _data.begin(), _data.end());
  is_ok = sendData(send_data, STATE_WRITE_FLASH, _msec_10) || sendData(send_data, STATE_WRITE_FLASH, 2 * _msec_10);

  return is_ok;
}

bool MEMSTCP::eraseRegister(const uint32_t _msec_10)
{
  return writeCMD(NET_CMD_REG_ERASE_FLASH, STATE_ERASE_REGISTER, _msec_10);
}

bool MEMSTCP::writeDownloadData(const std::string& _path)
{
  std::vector<uint32_t> all_address;
  std::vector<int32_t> all_value;
  bool is_ok = readDownloadData(_path, all_address, all_value);
  if (is_ok)
  {
    std::size_t reg_len = 60;
    std::vector<uint32_t> add_v;
    std::vector<int32_t> value_v;
    add_v.reserve(reg_len);
    value_v.reserve(reg_len);
    std::size_t num = all_address.size();
    for (std::size_t i = 0; i < num; ++i)
    {
      add_v.emplace_back(all_address.at(i));
      value_v.emplace_back(all_value.at(i));
      if (add_v.size() == reg_len || i == num - 1)
      {
        is_ok &= writeRegData(add_v, value_v, 40);
        add_v.clear();
        value_v.clear();
      }
    }
  }
  return is_ok;
}

bool MEMSTCP::checkDownloadData(const std::string& _path)
{
  std::vector<uint32_t> addr_of_file;
  std::vector<int32_t> value_of_file;

  if (!readDownloadData(_path, addr_of_file, value_of_file))
  {
    return false;
  }
  std::vector<int32_t> value_of_lidar;
  value_of_lidar.reserve(addr_of_file.size());
  std::size_t step_size =
    value_of_file.size() > MAX_REGISTER_SIZE_PER_TIME ? MAX_REGISTER_SIZE_PER_TIME : value_of_file.size();
  std::size_t step_total = value_of_file.size() / step_size;
  for (std::size_t i = 0; i < step_total; ++i)
  {
    std::vector<uint32_t> temp_addr;
    temp_addr.insert(temp_addr.end(), addr_of_file.begin() + static_cast<int>(i * step_size),
                     addr_of_file.begin() + static_cast<int>((i + 1) * step_size));
    std::vector<int32_t> temp_value;
    if (!readRegData(temp_addr, temp_value, 300))
    {
      RSFSCLog::getInstance()->error(
        msg_header_ + "checkDownloadData -> interrupted when getting value from lidar index: " + std::to_string(i));
      return false;
    }
    value_of_lidar.insert(value_of_lidar.end(), temp_value.begin(), temp_value.end());
  }
  if (step_size * step_total != value_of_file.size())
  {
    std::vector<uint32_t> temp_addr;
    temp_addr.insert(temp_addr.end(), addr_of_file.begin() + static_cast<int>(step_total * step_size),
                     addr_of_file.end());
    std::vector<int32_t> temp_value;
    if (!readRegData(temp_addr, temp_value, 200))
    {
      RSFSCLog::getInstance()->error(msg_header_ +
                                     "checkDownloadData -> interrupted when getting value from lidar last index");
      return false;
    }
    value_of_lidar.insert(value_of_lidar.end(), temp_value.begin(), temp_value.end());
  }
  if (value_of_file.size() != value_of_lidar.size())
  {
    RSFSCLog::getInstance()->error(
      msg_header_ + "checkDownloadData -> register's size read from lidar is not same as file: " +
      std::to_string(value_of_file.size()) + " VS " + std::to_string(value_of_lidar.size()));
    return false;
  }
  int check_failed_num = 0;
  for (std::size_t i = 0; i < value_of_file.size(); ++i)
  {
    if (value_of_file.at(i) != value_of_lidar.at(i))
    {
      check_failed_num++;
      RSFSCLog::getInstance()->warn(msg_header_ +
                                    "checkDownloadData -> error register : " + uintToHexString(addr_of_file.at(i)) +
                                    ", file value is " + uintToHexString(value_of_file.at(i)) + " but read value is " +
                                    uintToHexString(value_of_lidar.at(i)));
    }
  }
  if (0 != check_failed_num)
  {
    RSFSCLog::getInstance()->error(msg_header_ + "checkDownloadData -> total " + std::to_string(check_failed_num) +
                                   " register is error");
    return false;
  }
  return true;
}

void MEMSTCP::checkDeadline()
{
  // Check whether the deadline has passed. We compare the deadline against
  // the current time since a new asynchronous operation may have moved the
  // deadline before this actor had a chance to run.
  if (ptr_deadline_timer_->expires_at() <= boost::asio::deadline_timer::traits_type::now())
  {
    // The deadline has passed. The socket is closed so that any outstanding
    // asynchronous operations are cancelled. This allows the blocked
    // connect(), read_line() or write_line() functions to return.
    boost::system::error_code ignored_ec;
    ptr_socket_->close(ignored_ec);

    RSFSCLog::getInstance()->warn(std::string(static_cast<const char*>(__FUNCTION__)) +
                                  " -> async operation timeout : " + std::to_string(timeout_count_ms_) + "ms");

    // There is no longer an active deadline. The expiry is set to positive
    // infinity so that the actor takes no action until a new deadline is set.
    ptr_deadline_timer_->expires_at(boost::posix_time::pos_infin);
  }

  // Put the actor back to sleep.
  ptr_deadline_timer_->async_wait([this](const boost::system::error_code& _ec) {
    //unused
    (void)_ec;
    checkDeadline();
  });
}

void MEMSTCP::setAsyncTimeout(const std::size_t _timeout_count_ms)
{
  timeout_count_ms_ = _timeout_count_ms;
  ptr_deadline_timer_->expires_from_now(boost::posix_time::milliseconds(timeout_count_ms_));
}
}  // namespace lidar
}  // namespace robosense
