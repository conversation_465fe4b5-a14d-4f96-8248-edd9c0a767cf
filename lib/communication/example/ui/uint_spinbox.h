﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef UINT_SPINBOX_H
#define UINT_SPINBOX_H

#include <QtWidgets/QAbstractSpinBox>
class QWidget;

namespace robosense
{
namespace lidar
{
class UintSpinBox : public QAbstractSpinBox
{
  Q_OBJECT
public:
  explicit UintSpinBox(QWidget* _parent);
  explicit UintSpinBox(UintSpinBox&&)      = delete;
  explicit UintSpinBox(const UintSpinBox&) = delete;
  UintSpinBox& operator=(UintSpinBox&&) = delete;
  UintSpinBox& operator=(const UintSpinBox&) = delete;
  ~UintSpinBox() override                    = default;

private:
  void stepBy(const int _steps) override;
  QValidator::State validate(QString& _input, int& _pos) const override;
  quint32 value_ { 0 };
};
}  // namespace lidar
}  // namespace robosense
#endif  // UINT_SPINBOX_H