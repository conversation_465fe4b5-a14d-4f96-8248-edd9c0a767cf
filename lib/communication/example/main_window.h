﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <QtWidgets/QMainWindow>
#include <memory>

class QPushButton;
class QLineEdit;
class QSpinBox;

namespace robosense
{
namespace lidar
{
class MEMSTCP;

class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(int _argc, char** _argv, QWidget* _parent = nullptr);
  MainWindow(MainWindow&&)      = delete;
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  ~MainWindow() override;

protected Q_SLOTS:
  void slotTCPDisconnected();
  void slotConnectLidar();
  void slotUpdateAllWidgetState(const bool _is_lidar_connected);
  void slotReadReg();
  void slotWriteReg();
  void slotFixReg();
  void slotReset();
  void slotEraseRegister();
  void slotGetDownloadDataPath();
  void slotWriteDownloadData();
  void slotCheckDownloadData();

private:
  void setupLayout();

private:
  std::unique_ptr<MEMSTCP> ptr_mems_tcp_ { nullptr };

  QPushButton* pushbutton_connect_lidar_ { nullptr };
  QLineEdit* lineedit_lidar_ip_ { nullptr };
  QSpinBox* spinbox_msop_port_ { nullptr };

  QLineEdit* spinbox_reg_address_ { nullptr };  // TODO change to UintSpinBo;
  QSpinBox* spinbox_reg_value_ { nullptr };
  QPushButton* pushbutton_read_reg_ { nullptr };
  QPushButton* pushbutton_write_reg_ { nullptr };
  QPushButton* pushbutton_fix_reg_ { nullptr };
  QPushButton* pushbutton_reset_ { nullptr };
  QPushButton* pushbutton_erase_register_ { nullptr };

  QPushButton* push_button_download_data_path_ { nullptr };
  QLineEdit* line_edit_download_data_path_ { nullptr };
  QPushButton* push_button_write_download_data_ { nullptr };
  QPushButton* push_button_check_download_data_ { nullptr };
};

}  // namespace lidar
}  // namespace robosense

#endif  // MAIN_WINDOW_H