﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/*
 * @file   parse_udp_data.h
 * <AUTHOR> Chen (<EMAIL>), Melo Wang (<EMAIL>)
 * @brief     you can use ParseUDPData to analysis UDP data upload from lidar which defined by RoboSense
 * @version 1.0.0
 * @date 2021-11-27
 * 
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *  
 * You can not use, copy or spread without official authorization.
 * 
 * If you find any BUG or improvement in ParseUDPData, please contact the authors, so we can share your idea  
 * 
**/

#ifndef PARSE_UDP_DATA_H
#define PARSE_UDP_DATA_H
#include <atomic>
#include <condition_variable>
#include <memory>
#include <vector>

namespace robosense
{
namespace lidar
{
constexpr std::size_t G_GATHER_UDP_PKT_NUM = 700;

#define RS_SWAP_SHORT(x) ((((x)&0xFF) << 8) | (((x)&0xFF00) >> 8))
#define RS_SWAP_LONG(x)  ((((x)&0xFF) << 24) | (((x)&0xFF00) << 8) | (((x)&0xFF0000) >> 8) | (((x)&0xFF000000) >> 24))

class MEMSUDP;

class ParseUDPData
{
public:
  explicit ParseUDPData();
  explicit ParseUDPData(ParseUDPData&&)      = delete;
  explicit ParseUDPData(const ParseUDPData&) = delete;
  ParseUDPData& operator=(ParseUDPData&&) = delete;
  ParseUDPData& operator=(const ParseUDPData&) = delete;
  ~ParseUDPData();

  /*
   * @brief     start a udp thread and start to analysis data
   * 
   * @param     _ip                not used here
   * @param     _msop_port         local port
   * @return    true               start successfully
   * @return    false              start failed
  **/
  bool connect(const std::string& _ip, const uint16_t _msop_port);
  bool disconnect();
  bool isTimeout();

private:
  void parseData(const char* _data);  // analysis udp in here, this function will be call in another thread
  void timeoutCB();                   // timeout handle in here

  void getDistTVCArea(const char* _org_data);

private:
  std::unique_ptr<MEMSUDP> ptr_udp_input_ { nullptr };
  std::mutex mutex_data_;

public:
  std::vector<int> dist_vec;
  std::vector<int> tvc_vec;
  std::vector<int> area_vec;
  std::vector<int> ref_vec;

  std::size_t collected_num_n { 0 };
  std::size_t chn_num { 0 };
};
}  // namespace lidar
}  // namespace robosense

#endif  // PARSE_UDP_DATA_H