﻿set(EXAMPLE_NAME communication_example)

find_package(
  Qt5
  COMPONENTS Widgets Network Core
  REQUIRED)

# NOTE: use auto
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

add_executable(${EXAMPLE_NAME})

target_include_directories(${EXAMPLE_NAME} SYSTEM PRIVATE ${PROJECT_SOURCE_DIR} ${Qt5Widgets_INCLUDE_DIRS} ${Qt5Network_INCLUDE_DIRS})

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_compile_options(${EXAMPLE_NAME} PUBLIC /O2 /utf-8)
  target_link_libraries(${EXAMPLE_NAME} PRIVATE mems_communication Qt5::Core Qt5::Widgets Qt5::Network)
else()
  target_compile_options(${EXAMPLE_NAME} PUBLIC -fPIC -Wall -O3)
  target_link_libraries(${EXAMPLE_NAME} PRIVATE mems_communication Qt5::Core Qt5::Widgets Qt5::Network pthread)
endif()

target_sources(${EXAMPLE_NAME} PRIVATE example.cpp main_window.cpp)

set_target_properties(
  ${EXAMPLE_NAME}
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)