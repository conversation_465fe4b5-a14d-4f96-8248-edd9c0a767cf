﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "parse_udp_data.h"
#include "mems_udp.h"
#include <iostream>
#include <string>

namespace robosense
{
namespace lidar
{
// NOTE pragma中间这部分为具体的UDP数据结构定义，需要根据实际情况去定义
// 当你需要用到UDP解析时，你一般就会获取到对应的数据结构，如果没有，请咨询给你需求的人
#pragma pack(push, 1)

using RSM1ADCBlock = struct
{
  uint8_t rev[24];
};

using RSM1Block1 = struct
{
  uint16_t pitch_raw;
  uint16_t yaw_raw;
  struct
  {
    uint16_t dist;
    uint16_t ref;
  } dist_ref[5];
};

using RSM1Block2 = struct
{
  uint16_t pitch_raw;
  uint16_t yaw_raw;
  struct
  {
    uint16_t tvc;
    uint16_t area;
  } tvc_area[5];
};

using RSM1ADCPkt = struct
{
  uint32_t head;          //pkt_head
  uint16_t pkt_cnt;       //packet number
  uint16_t pkg_len;       //packet length
  uint8_t reserved1[22];  //reserved
  uint8_t lidar_type;     //默认值0x10
  uint8_t wave_mode;      //回波模式，单回波0x01，双0x02，采数模式0x03
  uint8_t mems_tmp;       //mems温度
  uint8_t time_mode;      //时间同步模式
  uint8_t timestamp[10];
  RSM1ADCBlock blocks[50];
  uint8_t reserved2[4];  //reserved
};

#pragma pack(pop)

ParseUDPData::ParseUDPData()
{
  ptr_udp_input_ = std::make_unique<MEMSUDP>(sizeof(RSM1ADCPkt));  // do not use a const number
  ptr_udp_input_->regRecvCallback([this](auto&& _p_h1) { parseData(std::forward<decltype(_p_h1)>(_p_h1)); });
  // ptr_udp_input_->regTimeoutCallback(std::bind(&ParseUDPData::timeoutCB, this)); //TODO: if need this callback?

  tvc_vec.reserve(32768);
  ref_vec.reserve(32768);
  dist_vec.reserve(32768);
  area_vec.reserve(32768);
}

ParseUDPData::~ParseUDPData() { ptr_udp_input_.reset(); }

bool ParseUDPData::connect(const std::string& _ip, const uint16_t _msop_port)
{
  return ptr_udp_input_->start(_ip, _msop_port);
}

bool ParseUDPData::disconnect() { return ptr_udp_input_->stop(); }

void ParseUDPData::timeoutCB()
{
  std::cout << "capture udp timeout, stop capture..." << std::endl;
  ptr_udp_input_->stop();
}

void ParseUDPData::parseData(const char* _data)
{
  const RSM1ADCPkt* pkt_data = reinterpret_cast<const RSM1ADCPkt*>(_data);
  if (pkt_data->head != 0x55aa5aa5 && pkt_data->head != 0xa55aaa55)
  {
    std::cout << "head of udp is not corrected: 0x" << std::hex << pkt_data->head << std::endl;
    return;
  }

  getDistTVCArea(_data);
}

void ParseUDPData::getDistTVCArea(const char* _org_data)
{
  if (chn_num > 4 || chn_num < 0 || collected_num_n > G_GATHER_UDP_PKT_NUM)
  {
    return;
  }

  const RSM1ADCPkt* p_org_data = reinterpret_cast<const RSM1ADCPkt*>(_org_data);
  for (std::size_t i = 0; i < 50; i++)
  {
    if (i % 2 == 0)
    {
      const RSM1Block1* block = reinterpret_cast<const RSM1Block1*>(&p_org_data->blocks[i]);
      uint16_t ref            = RS_SWAP_SHORT(block->dist_ref[chn_num].ref);
      uint16_t dist           = RS_SWAP_SHORT(block->dist_ref[chn_num].dist);
      std::unique_lock<std::mutex> lock(mutex_data_);
      ref_vec.emplace_back(ref);
      dist_vec.emplace_back(dist);
      lock.unlock();
    }
    else
    {
      const RSM1Block2* block = reinterpret_cast<const RSM1Block2*>(&p_org_data->blocks[i]);
      uint16_t tvc            = RS_SWAP_SHORT(block->tvc_area[chn_num].tvc);
      uint16_t area           = RS_SWAP_SHORT(block->tvc_area[chn_num].area);
      std::unique_lock<std::mutex> lock(mutex_data_);
      tvc_vec.emplace_back(tvc);
      area_vec.emplace_back(area);
      lock.unlock();
    }
  }

  ++collected_num_n;
}

bool ParseUDPData::isTimeout()
{
  //TODO: need is timeout?
  // return ptr_udp_input_->isTimeout();
  return false;
}

}  // namespace lidar
}  // namespace robosense