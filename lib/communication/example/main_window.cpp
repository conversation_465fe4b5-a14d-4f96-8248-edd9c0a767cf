﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include "include/mems_tcp.h"

#include <iostream>
#include <regex>

#include <QtCore/QCoreApplication>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>

namespace robosense
{
namespace lidar
{
/****************************************************************
 * @brief     check if the ip satisfy IPV4, according to https://zh.wikipedia.org/wiki/IPv4
 *
 * @param     _ip                 the ip to be checked
 * @return    true               the ip satisfy IPV4
 * @return    false              the ip is not satisfy IPV4
 ****************************************************************/
static bool checkIPV4(const std::string& _ip)
{
  std::string regex_ip = std::string(R"(^(1\d{2}|2[0-1]\d|22[0-3]|[1-9]\d|[1-9])\.)") +
                         std::string(R"((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.)") +
                         std::string(R"((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.)") +
                         std::string(R"((1\d{2}|2[0-4]\d|25[0-4]|[1-9]\d|\[1-9])$)");
  return std::regex_match(_ip, std::regex(regex_ip));
}

MainWindow::MainWindow(int _argc, char** _argv, QWidget* _parent) : QMainWindow(_parent)
{
  Q_UNUSED(_argc);
  Q_UNUSED(_argv);
  setupLayout();

  slotUpdateAllWidgetState(false);
}

MainWindow::~MainWindow()
{
  if (ptr_mems_tcp_)
  {
    ptr_mems_tcp_.reset();
  }
}

void MainWindow::slotConnectLidar()
{
  if (QString::fromUtf8("连接雷达") == pushbutton_connect_lidar_->text())
  {
    if (!checkIPV4(lineedit_lidar_ip_->text().toStdString()))
    {
      std::cout << "IP does not satisfy IPV4's rule" << std::endl;
      return;
    }
    if (ptr_mems_tcp_->connect(lineedit_lidar_ip_->text().toStdString(), spinbox_msop_port_->value()))
    {
      slotUpdateAllWidgetState(true);
      pushbutton_connect_lidar_->setText("断开雷达");
    }
    else
    {
      std::cout << "connect lidar failed" << std::endl;
    }
  }
  else
  {
    if (ptr_mems_tcp_->disconnect())
    {
      pushbutton_connect_lidar_->setText("连接雷达");
    }
    else
    {
      std::cout << "disconnect lidar failed" << std::endl;
    }
    slotUpdateAllWidgetState(false);
  }
}

void MainWindow::slotWriteReg()
{
  bool is_ok      = false;
  quint32 address = spinbox_reg_address_->text().toUInt(&is_ok, 16);
  if (!is_ok || spinbox_reg_value_->text().isEmpty())
  {
    std::cout << "写入数据不能为空" << std::endl;
    return;
  }
  // 正常获取到的寄存器都是32位对齐的，在writeRegData函数内部也会进行约束，这里由于控件使用lineedit，仅做提示用
  if (0U != (address % 4))
  {
    std::cout << "寄存器地址不合法" << std::endl;
    return;
  }
  if (!ptr_mems_tcp_->writeRegData(std::vector<quint32>({ address }),
                                   std::vector<qint32>({ spinbox_reg_value_->value() })))
  {
    std::cout << "写入错误" << std::endl;
  }
}

void MainWindow::slotReadReg()
{
  bool is_ok      = false;
  quint32 address = spinbox_reg_address_->text().toUInt(&is_ok, 16);
  if (!is_ok)
  {
    std::cout << "读取地址不能为空" << std::endl;
    return;
  }
  if (0U != (address % 4))
  {
    std::cout << "寄存器地址不合法" << std::endl;
    return;
  }
  std::vector<qint32> read_data;
  if (ptr_mems_tcp_->readRegData(std::vector<quint32>({ address }), read_data))
  {
    spinbox_reg_value_->setValue(read_data[0]);
  }
  else
  {
    std::cout << "读取错误" << std::endl;
  }
  //   QString firmware_path = "/home/<USER>/Desktop/mems_mirror_calib_regs/302300CC.bin";
  //   quint32 addr_offset = 0x0;
  //   if (ptr_mems_tcp_->writeToFlash(firmware_path, addr_offset))
  //   {
  //     std::cout << "固件升级成功" << std::endl;
  //   }
  //   else
  //   {
  //     std::cout << "固件升级失败" << std::endl;
  //   }
}

void MainWindow::slotTCPDisconnected()
{
  if (pushbutton_connect_lidar_->text() == QString::fromUtf8("连接雷达"))  // already disconnected
  {
    return;
  }
  pushbutton_connect_lidar_->setText("连接雷达");
  slotUpdateAllWidgetState(false);
  std::cout << "雷达断开" << std::endl;
}

void MainWindow::slotUpdateAllWidgetState(const bool _is_lidar_connected)
{
  spinbox_reg_address_->setEnabled(_is_lidar_connected);
  spinbox_reg_value_->setEnabled(_is_lidar_connected);
  pushbutton_read_reg_->setEnabled(_is_lidar_connected);
  pushbutton_write_reg_->setEnabled(_is_lidar_connected);
  pushbutton_fix_reg_->setEnabled(_is_lidar_connected);
  pushbutton_reset_->setEnabled(_is_lidar_connected);
  pushbutton_erase_register_->setEnabled(_is_lidar_connected);
  push_button_download_data_path_->setEnabled(_is_lidar_connected);
  push_button_write_download_data_->setEnabled(_is_lidar_connected);
  push_button_check_download_data_->setEnabled(_is_lidar_connected);
  lineedit_lidar_ip_->setEnabled(!_is_lidar_connected);
  spinbox_msop_port_->setEnabled(!_is_lidar_connected);
}

void MainWindow::slotFixReg()
{
  if (ptr_mems_tcp_->fixRegister())
  {
    std::cout << "固化成功" << std::endl;
  }
  else
  {
    std::cout << "固化失败" << std::endl;
  }
}

void MainWindow::slotReset()
{
  if (ptr_mems_tcp_->reset())
  {
    std::cout << "软启动成功, 请2s后重连MEMS." << std::endl;
  }
  else
  {
    std::cout << "软启动失败" << std::endl;
  }
}

void MainWindow::slotEraseRegister()
{
  if (ptr_mems_tcp_->eraseRegister())
  {
    std::cout << "擦除register成功" << std::endl;
  }
  else
  {
    std::cout << "擦除register失败" << std::endl;
  }
}
void MainWindow::slotGetDownloadDataPath()
{
  QString dir = QFileDialog::getExistingDirectory(this, tr("Open Directory"), QDir::homePath(),
                                                  QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
  line_edit_download_data_path_->setText(dir);
}

void MainWindow::slotWriteDownloadData()
{
  QString path = line_edit_download_data_path_->text();
  if (!path.isEmpty())
  {
    QDir dir(path);
    if (dir.exists())
    {
      QString data_file = path + "/download_data.csv";
      if (QFileInfo::exists(data_file) && QFileInfo(data_file).isFile())
      {
        std::vector<quint32> mems_ctrl_en_addr = { 0x83ca0000 };
        std::vector<qint32> mems_ctrl_en;
        bool flag = ptr_mems_tcp_->readRegData(mems_ctrl_en_addr, mems_ctrl_en);
        flag &= ptr_mems_tcp_->writeRegData(mems_ctrl_en_addr, std::vector<qint32>({ 0x0 }));
        flag &= ptr_mems_tcp_->writeDownloadData(path.toStdString());
        flag &= ptr_mems_tcp_->writeRegData(mems_ctrl_en_addr, mems_ctrl_en);
        if (flag)
        {
          std::cout << "Download data写入成功\n";
        }
        else
        {

          std::cout << "Download data写入失败\n";
        }
      }
    }
    else
    {
      QMessageBox::warning(this, QString("提示对话框"), path + QString("不存在，请重新输入"), QMessageBox::Ok);
    }
  }
}

void MainWindow::slotCheckDownloadData()
{
  QString path = line_edit_download_data_path_->text();
  QDir dir(path);
  QString data_file = path + "/download_data.csv";
  if (path.isEmpty() || !QFileInfo::exists(data_file) || !QFileInfo(data_file).isFile())
  {
    std::cout << "路径错误或不存在download_data.csv" << std::endl;
  }

  if (!ptr_mems_tcp_->checkDownloadData(path.toStdString()))
  {
    std::cout << "校验失败，请检查终端记录" << std::endl;
  }
  else
  {
    std::cout << "校验全部成功" << std::endl;
  }
}

void MainWindow::setupLayout()
{
  QHBoxLayout* layout_connect = new QHBoxLayout;
  lineedit_lidar_ip_          = new QLineEdit(QString::fromUtf8("*************"),
                                              this);  // NOTE you should save this value to QSetting, and read it after reload
  spinbox_msop_port_ = new QSpinBox(this);  // NOTE you should save this value to QSetting, and read it after reload
  spinbox_msop_port_->setMinimum(0);
  spinbox_msop_port_->setMaximum(65535);
  spinbox_msop_port_->setValue(6699);
  pushbutton_connect_lidar_ = new QPushButton(QString::fromUtf8("连接雷达"), this);
  layout_connect->addWidget(new QLabel(QString::fromUtf8("IP："), this));
  layout_connect->addWidget(lineedit_lidar_ip_);
  layout_connect->addWidget(new QLabel(QString::fromUtf8("MSOP："), this));
  layout_connect->addWidget(spinbox_msop_port_);
  layout_connect->addWidget(pushbutton_connect_lidar_);

  QHBoxLayout* layout_reg = new QHBoxLayout;
  spinbox_reg_address_    = new QLineEdit(this);
  spinbox_reg_address_->setText("83ca0000");
  spinbox_reg_value_ = new QSpinBox(this);
  spinbox_reg_value_->setPrefix("0x");
  spinbox_reg_value_->setDisplayIntegerBase(16);
  spinbox_reg_value_->setRange(0, 0x7FFFFFFF);

  pushbutton_read_reg_       = new QPushButton(QString::fromUtf8("读"));
  pushbutton_write_reg_      = new QPushButton(QString::fromUtf8("写"));
  pushbutton_fix_reg_        = new QPushButton(QString::fromUtf8("固化"));
  pushbutton_reset_          = new QPushButton(QString::fromUtf8("reset"));
  pushbutton_erase_register_ = new QPushButton(QString::fromUtf8("擦除"));

  layout_reg->addWidget(new QLabel("address: 0x", this));
  layout_reg->addWidget(spinbox_reg_address_);
  layout_reg->addWidget(new QLabel("value: ", this));
  layout_reg->addWidget(spinbox_reg_value_);
  layout_reg->addWidget(pushbutton_read_reg_);
  layout_reg->addWidget(pushbutton_write_reg_);
  layout_reg->addWidget(pushbutton_fix_reg_);
  layout_reg->addWidget(pushbutton_reset_);
  layout_reg->addWidget(pushbutton_erase_register_);

  push_button_download_data_path_ = new QPushButton("download data路径", this);
  line_edit_download_data_path_   = new QLineEdit(this);
  line_edit_download_data_path_->setEnabled(false);
  push_button_write_download_data_  = new QPushButton("写入", this);
  push_button_check_download_data_  = new QPushButton("校验", this);
  QHBoxLayout* layout_download_data = new QHBoxLayout;
  layout_download_data->addWidget(push_button_download_data_path_);
  layout_download_data->addWidget(line_edit_download_data_path_);
  layout_download_data->addWidget(push_button_write_download_data_);
  layout_download_data->addWidget(push_button_check_download_data_);

  QVBoxLayout* layout_main = new QVBoxLayout;
  layout_main->addLayout(layout_connect);
  layout_main->addLayout(layout_reg);
  layout_main->addLayout(layout_download_data);
  QWidget* widget_main = new QWidget(this);
  widget_main->setLayout(layout_main);
  this->setCentralWidget(widget_main);

  ptr_mems_tcp_ = std::make_unique<MEMSTCP>();
  QObject::connect(pushbutton_connect_lidar_, &QPushButton::pressed, this, &MainWindow::slotConnectLidar);
  QObject::connect(pushbutton_read_reg_, &QPushButton::pressed, this, &MainWindow::slotReadReg);
  QObject::connect(pushbutton_write_reg_, &QPushButton::pressed, this, &MainWindow::slotWriteReg);
  QObject::connect(pushbutton_fix_reg_, &QPushButton::pressed, this, &MainWindow::slotFixReg);
  QObject::connect(pushbutton_reset_, &QPushButton::pressed, this, &MainWindow::slotReset);
  QObject::connect(pushbutton_erase_register_, &QPushButton::pressed, this, &MainWindow::slotEraseRegister);
  QObject::connect(push_button_download_data_path_, &QPushButton::pressed, this, &MainWindow::slotGetDownloadDataPath);
  QObject::connect(push_button_write_download_data_, &QPushButton::pressed, this, &MainWindow::slotWriteDownloadData);
  QObject::connect(push_button_check_download_data_, &QPushButton::pressed, this, &MainWindow::slotCheckDownloadData);
}
}  // namespace lidar
}  // namespace robosense