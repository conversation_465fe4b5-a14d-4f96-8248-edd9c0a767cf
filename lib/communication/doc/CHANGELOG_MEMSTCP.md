﻿# Changelog
## v1.1.3 - 2023-05-10

### Fixed

- 在connect时获取雷达的网络信息,解决getNetworkInfo接口获取信息错误的问题.

## v1.1.2 - 2023-04-27

### Fixed

- 解决未调用connect时就调用isConnect导致崩溃的问题

## v1.1.1 - 2023-04-26

### Changed

- M2雷达硬件版本判定改为版本ID非236即为H版本
## v1.1.0 - 2023-04-13

### Changed
- 命令读写逻辑同步MEMS_TOOL，即每次发送读写命令前进入命令模式且读写完成后退出命令模式，兼容SU2220

### Added
- 增加硬件平台、固件版本、模组型号等内容获取接口
- sendData() 增加锁，避免多线程操作重复导致雷达回复数据错乱
  
## v1.0.2 - 2022-12-12

### Fixed
- fix a bug about read_data_buffer_, which may be reallocated when network is bad at the beginning, and data used to parse is the old one. See [#8](http://gitlab.robosense.cn/system_codebase/common_driver/mems_communication/issues/8#note_41520) for detail

## v1.0.1 - 2022-10-19

### Fixed

- 修复代码单词拼写的错误

## v1.0.0 - 20220829

### Added

+ First commit