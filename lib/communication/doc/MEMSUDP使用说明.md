﻿# MEMSUDP使用说明

用于获取、解析雷达上传的UDP数据。主要包含两个类：

1. **MEMSUDP**用于获取UDP数据
2. **demo**下的**ParseUDPData**是一个解析的Demo，主要有一个数据类**RSM1ADCPkt**用于将二进制的数据转换成有实际含义的数据，一个**parseData**函数被注册到UDP回调中进行数据解析

## 1 如何使用

1. `MEMSUDP`依赖于boost
2. `MEMSUDP`**依赖于pthread**，需要在target_link_libraries中增加`-lpthread`

需要在`CMakeLists.txt`中增加以下内容：

```cmake
if(WIN32)
  if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
    set(Boost_ARCHITECTURE "-x64")
  elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
    set(Boost_ARCHITECTURE "-x32")
  endif()
  set(Boost_USE_STATIC_LIBS ON)
  set(Boost_USE_MULTITHREADED ON)
  set(Boost_USE_STATIC_RUNTIME OFF)
endif(WIN32)

find_package(
  Boost
  COMPONENTS system date_time regex
  REQUIRED)

target_link_libraries(your_lib_or_exe ${Boost_LIBRARIES} -lpthread)
target_include_directories(your_lib_or_exe PRIVATE include
                                                     ${Boost_INCLUDE_DIRS})
```
然后：

1. 将mems_udp.cpp、parse_udp_data.cpp和头文件加到项目中
2. 在main_window头文件中定义一个parse_udp_data类的指针
   ```cpp 
	 std::unique_ptr<ParseUDPData> ptr_adc_data_
   ```
3. 在main_window 构造函数中
   ```cpp
   ptr_adc_data_.reset(new ParseUDPData());
   ```
4. 连接雷达时传入IP和MSOPPort，返回true则连接成功。注意不能与rs view等使用到**udp通讯**的软件同时连接雷达。
   ```cpp
	ptr_adc_data_->connect(std::string(widget_lidar_info_->getLidarIP()), short(widget_lidar_info_->getMSOPPort())))
   ```

## 2 各主要函数介绍

### 2.1 MEMSUDP

1. 使用到了boost库的udp通讯，启动通讯时开启一个线程 `dataProcess`，在这里面接收数据，代码参考`MEMSUDP::start`
2. `regRecvCallback`函数，将ParseUDPData构造函数中传递过来的` parseData`函数指针push到`vec_cb_`这个函数指针容器中，当获取到正常的数据时进行调用
3. `dataProcess`函数,开辟一个在parse_udp_data.cpp文件中定义的结构体（即UDP包）大小的内存。调用 `async_receive`会将接收到的数据放到这段内存当中，然后通过` iter(p_recv_buffer)`，传递到parse_udp_data中的 `parseData(const char* data)` 函数中，从而在parse_udp_data.cpp文件中进行解析文件的操作。
4. `checkDeadline`是boost中的一种定时器机制，当超时（默认值1秒）出现时会被调用，取消当前次socket操作，timeout回调

### 2.2 ParseUDPData

这个类主要是用来解析udp数据包，以及调用udp通讯接口

1. 在parse_udp_data头文件中定义一个mems_udp类的指针`ptr_udp_input_`
2. 定义一个与udp包匹配的结构体，例如`RSM1ADCPkt`，这里需要注意的是两个`#pragma`之间强制1字节对其，这部分是必须要有的，否则内存对其之后，数据就不对了。
3. 在构造函数中，初始化指针以及调用`MEMSUDP`中的`regRecvCallback`函数，`regRecvCallback`函数将ParseUDPData类中的函数指针传递到`MEMSUDP`类中
4. `parseData`函数会被注册到`MEMSUDP`线程中，用来判断接收到的数据帧头是否正确，如果正确，则使用你自己定义的解析函数，如`getDistTVCArea` 函数解析数据
5. `getDistTAPArea` 函数用来将数据分类，分为ref，dist，tvc，area四类。在这里可以将数据拷贝到其他线程进行处理，否则会导致`MEMSUDP`的接收线程来不及接收。

## 3 注意事项

1. 请做好线程同步处理，这部分的工作由`MEMSUDP`的调用者负责