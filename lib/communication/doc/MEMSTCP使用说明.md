﻿# MEMSTCP使用说明

本工程用于与MEMS进行TCP通信，主要用于读写寄存器

## 1 如何添加到工程中

`MEMSTCP`依赖于Boost，如果你已经装了ROS，那直接使用系统自带的即可。

将`mems_tcp.h`和`mems_tcp.cpp`添加到你的工程，按照Qt相关编译方法进行编译即可

## 2 基本框架

TCP通信协议由PS的Hen<PERSON> Zhong（钟涛）实现，具体协议如[LidarTCP通信协议](doc/MEMS_Lidar_TCP通信协议.docx)。

### 2.1 设计原理

使用`QTcpSocket`实现TCP通信功能，雷达的数据回传在`slotReadTCPData`中进行解析。根据协议内容，当写寄存器时，雷达会返回一个确认信息；当读寄存器时，雷达会按地址返回寄存器的值。`slotReadTCPData`中根据返回数据的包头进行不同的解析。

这里的延时，为了提高细粒度，是使用每次延迟10ms，延迟多次的方式进行的。这样的好处是，如果直接延时100ms，那么哪怕1ms就完成了读写，也至少要等到100ms才能返回。但如果分成10个10ms，那么有可能在10ms就可以完成退出了。

### 2.2 使用方法

1. `MEMSTCP`依赖于boost
2. `MEMSTCP`**依赖于pthread**，需要在target_link_libraries中增加`-lpthread`

需要在`CMakeLists.txt`中增加以下内容：

```cmake
if(WIN32)
  if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
    set(Boost_ARCHITECTURE "-x64")
  elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
    set(Boost_ARCHITECTURE "-x32")
  endif()
  set(Boost_USE_STATIC_LIBS ON)
  set(Boost_USE_MULTITHREADED ON)
  set(Boost_USE_STATIC_RUNTIME OFF)
endif(WIN32)

find_package(
  Boost
  COMPONENTS system date_time regex
  REQUIRED)

target_link_libraries(your_lib_or_exe ${Boost_LIBRARIES} -lpthread)
target_include_directories(your_lib_or_exe PRIVATE include
                                                     ${Boost_INCLUDE_DIRS})
```


### 2.3 注意事项

1. `(std::vector<quint32>({address})`使用std::vector的构造来传参，一定要加花括号`{}`，否则会造成读写不正常
2. 默认情况下，读写寄存器都会等待20个10ms，如果200ms无反馈则认为通信失败，如果你的数据量比较大或者通信比较拥堵，建议在函数中设置相应的参数值。
3. 读写寄存器一次不能超过60个（该限制目前已取消，该限制来源于早前的程序，并非MEMSTCP，目前MEMSTCP已在反标中验证过非常多的雷达，都可以支持每次至少128个寄存器的写入。读取目前没进行过大量的测试，如果数据量非常大，`MEMSTCP`的使用者有义务将这些数据分批进行读写）

## 3. Q&A

## Q1 我该怎么快速地上手呢

**A:** example和头文件的注释已经足够充分让你理解这个工程的作用和怎么使用，最快的方法就是阅读这些代码和注释，比问人都快。
