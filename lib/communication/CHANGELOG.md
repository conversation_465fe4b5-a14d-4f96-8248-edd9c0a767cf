﻿# Changelog
## v1.1.7 - 2025-01-13
### Feat
- 增加MX数字版的识别以及雷达类型

## v1.1.6 - 2024-10-21

### Feat
- 获取下载文件路径如果传入完整文件路径则使用完整路径

## v1.1.5 - 2024-09-03

### Feat
- 增加udp用户定义层的设置
## v1.1.4 - 2024-06-15

### Fixed
- Feat:mx新的硬件版本号为0x60,暂时先定为M2类型

## v1.1.3 - 2023-05-10

### Fixed

- 在connect时获取雷达的网络信息,解决getNetworkInfo接口获取信息错误的问题.

## v1.1.2 - 2023-04-27

### Fixed

- 解决未调用connect时就调用isConnect导致崩溃的问题

## v1.1.1 - 2023-04-26

### Changed

- M2雷达硬件版本判定改为版本ID非236即为H版本
## v1.1.0 - 2023-04-13

### Changed
- 命令读写逻辑同步MEMS_TOOL，即每次发送读写命令前进入命令模式且读写完成后退出命令模式，兼容SU2220

### Added
- 增加硬件平台、固件版本、模组型号等内容获取接口
- sendData() 增加锁，避免多线程操作重复导致雷达回复数据错乱
  
## v1.0.2 - 2022-12-12

### Fixed
- fix a bug about read_data_buffer_, which may be reallocated when network is bad at the beginning, and data used to parse is the old one. See [#8](http://gitlab.robosense.cn/system_codebase/common_driver/mems_communication/issues/8#note_41520) for detail

## v1.0.1 - 2022-10-19

### Fixed

- 修复代码单词拼写的错误

## v1.0.0 - 20220829

### Added

+ First commit