﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MEMS_COMMUNICATION_COMMON_H
#define MEMS_COMMUNICATION_COMMON_H

#include "rsfsc_log/rsfsc_log.h"

#include <cstddef>
#include <cstdint>
#include <fstream>
#include <iostream>
#include <memory>

#include <array>
#include <atomic>
#include <boost/asio.hpp>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

namespace robosense
{
namespace lidar
{
constexpr uint32_t PL_FIRMWARE_VERSION_ADDR = 0x83c00000;  // PL端的固件版本号
constexpr uint32_t PS_FIRMWARE_VERSION_ADDR = 0x83c40800;  // PS端的固件版本号
constexpr uint32_t LASER_MODULE_ADDR        = 0x83c0000c;  // 模组版本信息寄存器地址
constexpr uint32_t FIX_MIRROR_ADDR          = 0x83ca0000;  // 关闭振镜，0关闭，1打开
constexpr uint32_t UPLOAD_CLOSE_ADDR        = 0x83c00074;  // 点云数据上传，0关闭，1打开

enum LidarHardwareSerial
{
  UNKNOWN_LIDAR_HARDWARE_SERIAL = 0,
  M1,
  M1P,
  M2,
  MX
};

enum LidarSoftwareVersion
{
  UNKNOWN_LIDAR_SOFTWARE_SERIAL = 0,
  PRODUCTION_VERSION,
  CUSTOM_VERSION,
};

enum LidarFunctionSafeVersion
{
  UNKNOWN_LIDAR_FUNCTION_SAFE_VERSION = 0,
  NOT_FUNCTION_SAFE_VERSION,
  FUNCTION_SAFE_VERSION
};

enum LaserModuleVersion
{
  UNKNOWN_LASER_MODULE_VERSION = 0,
  M2_AD_VERSION,
  M2_H_VERSION
};

enum DigitalBoardVersion
{
  UNKNOWN_DIGITAL_BOARD_VERSION = 0,
  M2_DIGITAL_BOARD_1V2,
  M2_DIGITAL_BOARD_1V3,
  M2_DIGITAL_BOARD_1V4,
  M2_DIGITAL_BOARD_3V0,
  M2_DIGITAL_BOARD_1V5,
  M1P_DIGITAL_BOARD_VAVE_010,
  MX_DIGITAL_BOARD_1V345_V12,
  MX_DIGITAL_BOARD_1V345_V34
};

const std::vector<std::string> DIGITAL_BOARD_VERSION_STR({
  "UNKNOWN_DIGITAL_BOARD_VERSION",
  "M2_DIGITAL_BOARD_1V2",
  "M2_DIGITAL_BOARD_1V3",
  "M2_DIGITAL_BOARD_1V4",
  "M2_DIGITAL_BOARD_3V0",
  "M2_DIGITAL_BOARD_1V5",
  "M1P_DIGITAL_BOARD_VAVE_010",
  "MX_DIGITAL_BOARD_1V345_V12",
  "MX_DIGITAL_BOARD_1V345_V34"
});

enum DigitalBoardRegisterValue
{
  M1P_VAVE = 0b010
};

#pragma pack(1)
typedef struct
{
  uint8_t mac_address[6];  // lidar's MAC
  uint8_t mems_ip[4];      // lidar's ip
  uint8_t host_ip[4];      // lidar will send data to this ip

  uint16_t difop_port;  // Device Info Output Protocol, lidar driver will capture this data to get device info
  uint16_t msop_port;   // Main data Stream Output Protocol, tcp connect to this port

  uint16_t sn[3];  // lidar's sn

  uint8_t subnet_mask[4];  // lidar's net mask
  uint8_t gateway[4];      // lidar's gateway

  uint8_t ptp_port;  //lidar's PTP port
} NetworkInfo;
#pragma pack()

inline std::string uintToHexString(const uint32_t _data)
{
  std::string str_info;
  std::stringstream ss;
  ss << std::hex << _data;
  ss >> str_info;
  return "0x" + str_info;
}

inline std::vector<int> splitAndConvertToInts(const std::string& _ip)
{
  std::vector<int> result;
  std::istringstream iss(_ip);
  std::string token;

  while (std::getline(iss, token, '.'))
  {
    int value = std::stoi(token);  // 将分割后的字符串转换为整数
    result.push_back(value);
  }

  return result;
}

inline std::string getDownloadDataFileName(const std::string& _path)
{
  if (_path.back() == '/')
  {
    return _path + "download_data.csv";
  }
  else if (_path.find(".csv") != std::string::npos)
  {
    return _path;
  }

  return _path + "/download_data.csv";
}

inline void writeRegisterDataToFile(const std::vector<uint32_t>& _reg_addr,
                                    const std::vector<int32_t>& _reg_val,
                                    const std::string& _path)
{
  std::string str;
  for (std::size_t i = 0; i < _reg_addr.size(); ++i)
  {
    str += uintToHexString(_reg_addr[i]) + "," + uintToHexString(static_cast<uint32_t>(_reg_val[i])) + "\r\n";
  }
  std::string download_data_file = getDownloadDataFileName(_path);
  std::ofstream file(download_data_file, std::ios_base::out | std::ios_base::app);
  file << str;
}

inline bool readDownloadData(const std::string& _path, std::vector<uint32_t>& _addr, std::vector<int32_t>& _value)
{
  bool is_ok                     = true;
  std::string download_data_file = getDownloadDataFileName(_path);
  std::ifstream csv_file(download_data_file);
  if (!csv_file.good())
  {
    RSFSCLog::getInstance()->error("readDownloadData -> open file: " + download_data_file + " failed.");
    return false;
  }
  _addr.clear();
  _value.clear();

  std::string line;
  int line_num = 0;
  while (std::getline(csv_file, line))
  {
    ++line_num;
    if (line.empty())
    {
      RSFSCLog::getInstance()->error("readDownloadData -> get a empty line from download data file, line number: " +
                                     std::to_string(line_num));
      is_ok = false;
      break;
    }
    std::istringstream ss(line);
    std::vector<std::string> fields;
    std::string field;
    while (std::getline(ss, field, ','))
    {
      fields.emplace_back(field);
    }
    if (2 != fields.size())
    {
      RSFSCLog::getInstance()->error("readDownloadData -> each line of download data file must only have 2 "
                                     "elements: 'address,value'. but get: " +
                                     line);
      is_ok = false;
      break;
    }
    try
    {
      _addr.emplace_back(static_cast<uint32_t>(std::stoul(fields.at(0), nullptr, 16)));
      _value.emplace_back(static_cast<int32_t>(std::stoul(fields.at(1), nullptr, 16)));
    }
    catch (const std::exception& e)
    {
      RSFSCLog::getInstance()->error("readDownloadData -> failed to parse register csv of line " +
                                     std::to_string(line_num) + " with data: " + line);
      RSFSCLog::getInstance()->error("readDownloadData -> with exception: " + std::string(e.what()));
      is_ok = false;
      break;
    }
  }
  csv_file.close();
  RSFSCLog::getInstance()->info("readDownloadData -> total line number of download_data file: " +
                                std::to_string(line_num));
  return is_ok;
}
/**
   * @brief when connecting multiple MEMS with the same IP one by one, 
   *        delete previous ARP entry, which will speed up connect the next MEMS
   *        invoke it before connect MEMS
   *
   * @param _ip which address use to delete ARP table entry  
   */
inline void deleteARPCache(const std::string& _ip)
{
  std::vector<std::string> cmd_v;
#ifdef __linux__
  cmd_v.assign({ "arp -n", "sudo arp -d " + _ip, "arp -n" });
#elif _WIN32
  cmd_v.assign({ "arp -a", "arp -d " + _ip, "arp -a" });
#endif
  for (const auto& cmd : cmd_v)
  {
    int res = system(cmd.c_str());
    RSFSCLog::getInstance()->info("MEMSTCP::deleteARPCache -> return value: {}", res);
  }
}

inline void parseLaserModuleAndDigitalBoardInfo(int32_t _reg_val,
                                                LaserModuleVersion& _laser_module_version,
                                                DigitalBoardVersion& _digital_board_version)
{
  _laser_module_version  = LaserModuleVersion::UNKNOWN_LASER_MODULE_VERSION;
  _digital_board_version = DigitalBoardVersion::UNKNOWN_DIGITAL_BOARD_VERSION;
  std::string str_laser_module;
  std::stringstream ss;
  ss << std::hex << _reg_val;
  ss >> str_laser_module;
  RSFSCLog::getInstance()->info("MEMSTCP::parseLidarInfo -> Laser module version value is 0x" + str_laser_module);

  union LaserModuleUnion
  {
    int32_t reg_val;
    struct
    {
      uint8_t laser_module_version_info;
      uint8_t unknown_info_1;
      uint8_t unknown_info_2;
      uint8_t unknown_info_3;
    } laser_module;
  };

  LaserModuleUnion laser_module_version {};
  laser_module_version.reg_val = _reg_val;
  uint8_t module_version_info  = (laser_module_version.laser_module.laser_module_version_info & 0x0fU);
  switch (module_version_info)
  {
  case 0x02:
    _laser_module_version  = LaserModuleVersion::M2_AD_VERSION;
    _digital_board_version = DigitalBoardVersion::M2_DIGITAL_BOARD_1V2;
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Laser module version: M2_AD_VERSION");
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Digital board version: M2_DIGITAL_BOARD_1V2");
    break;
  case 0x03:
  case 0x06:
    _laser_module_version  = LaserModuleVersion::M2_AD_VERSION;
    _digital_board_version = DigitalBoardVersion::M2_DIGITAL_BOARD_1V3;
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Laser module version: M2_AD_VERSION");
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Digital board version: M2_DIGITAL_BOARD_1V3");
    break;
  case 0x08:
    _laser_module_version  = LaserModuleVersion::M2_H_VERSION;
    _digital_board_version = DigitalBoardVersion::M2_DIGITAL_BOARD_3V0;
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Laser module version: M2_AD_VERSION");
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Digital board version: M2_DIGITAL_BOARD_1V5");

    break;
  case 0x09:
  case 0x0A:
    _laser_module_version  = LaserModuleVersion::M2_H_VERSION;
    _digital_board_version = DigitalBoardVersion::M2_DIGITAL_BOARD_1V5;
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Laser module version: M2_AD_VERSION");
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Digital board version: M2_DIGITAL_BOARD_1V5");

    break;
  default:
    _laser_module_version  = LaserModuleVersion::M2_H_VERSION;
    _digital_board_version = DigitalBoardVersion::M2_DIGITAL_BOARD_1V4;
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Laser module version: M2_H_VERSION");
    RSFSCLog::getInstance()->info("MEMSTCP::parseLaserModuleInfo -> Digital board version: M2_DIGITAL_BOARD_1V4");
    break;
  }
}

/**
  * @brief parse lidar PL firmware version number
  * 
  * @param _pl_firmware_reg_val      Lidar PL firmware version number 
  * @param _hardware_serial          Lidar hardware serial info, e.g. M1, M1P, M2
  * @param _software_version         Lidar software version info, e.g. production version or custom version
  * @param _func_safe_version        Lidar function safe version info, e.g. function safe version or not function safe version
  */
inline void parseLidarInfo(int32_t _pl_firmware_reg_val,
                           LidarHardwareSerial& _hardware_serial,
                           LidarSoftwareVersion& _software_version,
                           LidarFunctionSafeVersion& _func_safe_version)
{
  _hardware_serial   = LidarHardwareSerial::UNKNOWN_LIDAR_HARDWARE_SERIAL;
  _software_version  = LidarSoftwareVersion::UNKNOWN_LIDAR_SOFTWARE_SERIAL;
  _func_safe_version = LidarFunctionSafeVersion::UNKNOWN_LIDAR_FUNCTION_SAFE_VERSION;

  std::string str_pl_firmware;
  std::stringstream ss;
  ss << std::hex << _pl_firmware_reg_val;
  ss >> str_pl_firmware;

  RSFSCLog::getInstance()->info("parseLidarInfo -> PL firmware is 0x" + str_pl_firmware);
  union PLFirmwareUnion
  {
    int32_t reg_val;
    struct
    {
      uint8_t software_version_info;
      uint8_t unknown_info;
      uint8_t project_info;
      uint8_t hardware_serial_info;
    } pl_firmware;
  };

  PLFirmwareUnion pl_version_union {};
  pl_version_union.reg_val = _pl_firmware_reg_val;
  switch (pl_version_union.pl_firmware.hardware_serial_info)
  {
  case 0x30:
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar hardware serial: M1");
    _hardware_serial = LidarHardwareSerial::M1;
    if (_pl_firmware_reg_val > 0x30230000 && pl_version_union.pl_firmware.software_version_info != 0xcc)
    {
      _func_safe_version = LidarFunctionSafeVersion::FUNCTION_SAFE_VERSION;
      RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar function safe version: FUNCTION_SAFE_VERSION");
    }
    else
    {
      _func_safe_version = LidarFunctionSafeVersion::NOT_FUNCTION_SAFE_VERSION;
      RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar function safe version: NOT_FUNCTION_SAFE_VERSION");
    }
    break;
  case 0x40:
  case 0x41:
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar hardware serial: M1P");
    _hardware_serial = LidarHardwareSerial::M1P;
    if (_pl_firmware_reg_val > 0x40020400)
    {
      _func_safe_version = LidarFunctionSafeVersion::FUNCTION_SAFE_VERSION;
      RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar function safe version: FUNCTION_SAFE_VERSION");
    }
    else
    {
      _func_safe_version = LidarFunctionSafeVersion::NOT_FUNCTION_SAFE_VERSION;
      RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar function safe version: NOT_FUNCTION_SAFE_VERSION");
    }
    break;
  case 0x50:
  case 0x60:
    _hardware_serial = LidarHardwareSerial::M2;
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar hardware serial: M2");
    break;
  default:
    _hardware_serial = LidarHardwareSerial::UNKNOWN_LIDAR_HARDWARE_SERIAL;
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar hardware serial: UNKNOWN");
    break;
  }

  switch (pl_version_union.pl_firmware.software_version_info)
  {
  case 0xcc:
    _software_version = LidarSoftwareVersion::PRODUCTION_VERSION;
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar software version: PRODUCTION_VERSION");
    break;
  default:
    _software_version = LidarSoftwareVersion::CUSTOM_VERSION;
    RSFSCLog::getInstance()->info("parseLidarInfo -> Lidar software version: CUSTOM_VERSION");
    break;
  }
}

/**
  * @brief checking if the lidar is VAVE-Type
  *
  * @param _laser_module_reg_val      Lidar module register value (0x83c0000C)
  * @param _lidar_digital_version     Lidar digital version, if the check condition is true,
  *                                   it will be set to M1P_DIGITAL_BOARD_VAVE_010.
  */
inline void checkIsVAVE(const uint32_t _laser_module_reg_val,
                        const LidarHardwareSerial _lidar_hardware_serial,
                        DigitalBoardVersion& _lidar_digital_version)
{
  if (LidarHardwareSerial::M1P == _lidar_hardware_serial &&
      DigitalBoardRegisterValue::M1P_VAVE == (_laser_module_reg_val & 0x0f))
  {
    _lidar_digital_version = DigitalBoardVersion::M1P_DIGITAL_BOARD_VAVE_010;

    RSFSCLog::getInstance()->info(
      std::string(__func__) +
      " -> this M1P lidar is VAVE-Type, laser module reg value is : " + std::to_string(_laser_module_reg_val));
    RSFSCLog::getInstance()->info(std::string(__func__) +
                                  " -> Lidar degital version set to: M1P_DIGITAL_BOARD_VAVE_010");
  }
}

inline void checkMxDigital(const uint32_t _digital_reg_val,
                           DigitalBoardVersion& _lidar_digital_version)
{
  if (_digital_reg_val == 0x1 || _digital_reg_val == 0x2)
  {
    _lidar_digital_version = DigitalBoardVersion::MX_DIGITAL_BOARD_1V345_V12;
  }
  else if (_digital_reg_val == 0x3 || _digital_reg_val == 0x4)
  {
    _lidar_digital_version = DigitalBoardVersion::MX_DIGITAL_BOARD_1V345_V34;
  }
  else
  {
    _lidar_digital_version = DigitalBoardVersion::UNKNOWN_DIGITAL_BOARD_VERSION;
  }

  RSFSCLog::getInstance()->info(std::string(__func__) +
                                " -> Lidar degital version set to: {}", DIGITAL_BOARD_VERSION_STR[_lidar_digital_version]);
}

}  // namespace lidar
}  // namespace robosense
#endif