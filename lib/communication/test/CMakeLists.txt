﻿set(BUILD_GMOCK
OFF
CACHE BOOL "disable gmock build")

# 修改 MD 为 MT
if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
set(CMAKE_CXX_FLAGS_RELEASE "/MT")
set(CMAKE_CXX_FLAGS_DEBUG "/MTd")
endif()

add_subdirectory(googletest EXCLUDE_FROM_ALL)
add_executable(test_main test.cpp)

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_link_libraries(test_main gtest)
else()
  target_link_libraries(test_main gtest pthread)
endif()

target_include_directories(test_main PRIVATE ${PROJECT_SOURCE_DIR}/include ${PROJECT_SOURCE_DIR}/src)
target_include_directories(test_main SYSTEM PRIVATE ${GTEST_INCLUDE_DIRS})
set_target_properties(
test_main
PROPERTIES CXX_STANDARD 14
         CXX_STANDARD_REQUIRED YES
         CXX_EXTENSIONS NO)

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_compile_options(test_main PRIVATE /utf-8)
else()
  target_compile_options(test_main PRIVATE -fPIC -Wall -O3)
endif()