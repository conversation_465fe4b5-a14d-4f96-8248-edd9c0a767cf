﻿## v1.2.0 - 20250513
### Added
+ 增加`setSubUnicode`,`setSubSn`,`getSubUnicode`,`getSubSn`,`setSubUnicodePos`,`setSubSnPos`,`getSubInfos`接口支持子件唯一码和SN的输入
  
## v1.1.9 - 20241104
### Fixed
+ 输入雷达SN完成后先将Shift+数字键对应的符号替换为数字，再触发雷达SN输入完成信号
  
## v1.1.8 - 20240827
### Fixed
+ 修复线束SN输入完成后未触发输入完成信号的BUG

## v1.1.7 - 20240826
### Added
+ 增加`WidgetLidarInfo::setLidarSNWidgetFocus()`和`WidgetLidarInfo::setCableSNWidgetFocus()`接口用于设置光标焦点

## v1.1.6 - 20230321
### Added
+ 增加`setFixedLidarInstallPosition`接口用于部分不需要使用安装位置的软件，如模组

## v1.1.5 - 20221019
### Added
+ 增加获取雷达信息操作必须在雷达过站检查之后限制，防止项目编号获取错误(暂未启用)

## v1.1.4 - 20220926
### Added
+ 增加获取SN后缀的接口用于连接项目编号过站时获取车型等信息
+ 增加根据雷达序号保存及加载线束SN功能
  
### Changed
+ 将SN控件的扫码枪输入延时从100ms加到400ms，以适应HoneyWell的扫码枪，否则HoneyWell扫码枪会输入超时
+ 修改了获取SN的接口名称

### Fixed
+ 修复021x_na项目编号无法识别的BUG

## v1.1.3 - 20220919
### Added
+ 增加线束管理控件
+ 增加`setCableSNPos`接口用于设置线束SN控件
+ 增加`setCableUseTimesInfoPos`接口用于设置线束使用寿命信息控件

## v1.1.2 - 20220908
### Added
+ 增加`resetIPandPort`和`setIPandPortAccording2Index`接口

### Fixed
+ 修改021A项目的默认雷达类型AY修改为00以兼容新老SN
## v1.1.1 - 20220829
### Fixed
+ 将FocusProxy设置成SN输入框，方便一拖多时Focus跳转。解决原来无法扫完一个无法设置跳转，要鼠标再点一下的BUG

## v1.1.0 - 20220819
### Add
+ 兼容现有SN规则，将现有SN后缀统一转移到车型和安装位置控件
+ 增加`signalProjectCodeChanged`接口

### Changed
+ 更新部分项目SN识别规则

## v1.0.0 - 20220730
### Added
+ 在原有LineeditLimitKeyboard的基础上增加了项目编号、车型、雷达安装位置、IP、端口号等信息

### Changed
+ 将原有项目编号识别相关代码从LineeditLimitKeyboard移到WidgetLidarInfo中，让扫码枪控件更纯粹

## v0.2.4 - 20220715
### Added
+ 增加0250_na-0255

## v0.2.3 - 20220323
### Added

+ 增加`setInputData`和`setFixedProjectCode`接口
+ 需要注意`G_MEMS_PROJECT_CODE_STR`越界情况

### Changed
+ 将`getProjectCode`接口修改为private，这个接口已经多次用错，直接禁用，后续通过`void signalInputFinished(int _pc);`来修改
+ 删除原有021G-021Z，替换为0220-022F，将0210_m1p_na修改为0220_na

## v0.2.2 - 20220207
### Added

+ 增加021F-021Z

## v0.2.1 - 20211122
### Added

+ 增加项目编号0210_NA~021E
+ 增加项目SN约束

## v0.2.0 - 20211010

+ 从widget_log中独立出来进行changelog
