﻿# WidgetLidarInfo使用手册

`WidgetLidarInfo`这个控件主要包含：

1. 雷达SN扫码枪输入控件（LineEditLimitKeyboard，不限位数）
2. 项目编号控件（QComboBox，参考ProjectCode）
3. 车型控件（QLineEdit，2位的字符）
4. 雷达安装位置控件（QComboBox，参考LidarInstallPosition）
5. 雷达IP控件（QLineEdit）
6. 雷达MSOP控件（QSpinBox）
7. 雷达DIFOP控件（QSpinBox，大部分软件用不到）
8. 雷达参数路径控件（QLineEdit，雷达特有参数，大部分软件用不到）
9. 雷达pcap路径控件（QLineEdit，选择一个pcap来解析，大部分软件用不到）
10. 线束SN扫码枪输入控件（LineEditLimitKeyboard）
11. 线束使用已用次数显示控件（QSpinBox）
12. 线束使用寿命次数设置控件（QSpinBox）
13. 多个子件唯一码（QLineEdit, 由于子件可能来自不同的厂家,所以不做字符限制）
14. 多个子件Sn（QLineEdit, 由于子件可能来自不同的厂家,所以不做字符限制）
    
由于使用速腾内部统一SN规则后，部分信息（项目编号、车型、雷达安装位置）需要通过与MES通信获取；而上了IP池后，部分信息（雷达IP、雷达MSOP、雷达DIFOP）需要通过与IP池服务器通信获取。为了最大可能减少大家的代码重复，减少大家的工作量，做出这个统一的控件，自动与MES、IP池服务器通信，修改完相关信息后，再提供软件进行使用

## 1 控件显示

`WidgetLidarInfo`就是继承于QWidget的一个普通控件，如果你使用代码方式编写UI，则直接new一个即可；如果你使用Designer来设计界面，则参考`控件提升`的一些方法进行提升即可，可参考之前`LineeditLimitKeyboard`的相关文档，不再赘述。

### 1.1 控件布局

如开头所述`WidgetLidarInfo`由9个控件组成，其内部使用`QGridLayout`进行布局，考虑到每个软件的布局不一样，且每个人的审美不同，所以每个子控件的布局是完全开放的，你只需要调用`setXXXPos`这几个函数即可轻松设置。

`setXXXPos`的前4个参数跟`QGridLayout::addWidget`的后4个参数是完全一样的，其中：

+ _row表示放在GridLayout的第几行（从0开始）
+ _column表示放在GridLayout的第几列（从0开始）
+ _row_span表示这个控件占几行，默认是占1行
+ _column_span表示这个控件占几列，默认是占1列
+ _show_title表示是否显示title，建议显示title方便员工辨识，但出于紧凑等目的，且控件不会导致歧义的情况下，也可以不显示。但如果出现歧义，例如同时有DIFOP和MSOP你不显示就容易误解

### 1.2 一拖多时焦点跳转

当一拖多时，员工扫完1控件的码，软件应该自动将焦点设置为下一个2控件，2扫完，自动跳转到3控件。这样子员工能连续输入，不用输入一次点击一次鼠标。设置方法如下：

```cpp
QObject::connect(widget_lidar_info_vec_[index], &rsfsc_lib::WidgetLidarInfo::signalLidarNameInputFinished,
                 widget_lidar_info_vec_[index + 1],
                 static_cast<void (rsfsc_lib::WidgetLidarInfo::*)()>(&rsfsc_lib::WidgetLidarInfo::setFocus));
// 注意index + 1越界问题
```

## 2 控件使用

### 2.1 正常使用顺序

控件配合`WidgetLogSetting`的`registerWidgetLidarInfo`来使用，只要注册到`WidgetLogSetting`里，就默认将控制权交出，**一般情况下就不要再操作这个控件**，避免重复操作或者流程错误导致异常。例如你提前清空了SN，则可能会导致程序结束异常。

1. new一个控件，按照第1章内容布局好子控件
2. 如果你希望部分项目不校验SN规则，则调用`setNotCheckSNProject`来进行设置，例如模组中`setNotCheckSNProject("0210;0220")`
3. 如果你希望固定项目编号，则调用`setFixedProjectCode`和`setFixedLidarInstallPosition`，否则项目编号会根据SN后缀或者MES信息改变。同样是模组软件会常常用到
4. 注册`signalLidarNameInputFinished`的回调，当扫码完毕会触发这个signal，收到这个信号后，调用`WidgetLogSetting::checkLogState`和其他初始化操作。
5. 调用`getNetWorkInfo`等信息连接雷达，需要注意的是，**必须要等checkLogState返回后再连接雷达**，否则可能是用的错误的IP和端口号连接
6. 显式析构
   
**注：**
+ 最新版的MEMSTCP可以直接用`getNetworkInfo`返回的值作为参数进行连接
+ 要确保控件正常析构，在析构时会保存当前值，下次重启时加载上次设置值，如不正常析构会导致无法保存上次值。可显示析构确保正常析构

### 2.1 项目编号等控件修改逻辑

![](img/widget_project_code_flow.svg)

在调用`WidgetLogSetting::finishProcess`后，SN控件会被自动清空，其他则会被保持不变，防止员工没有扫下一个码就开始测试

### 2.2 IP等控件修改逻辑

1. 过站时判断是否勾选启用IP池，启用的话则会去获取IP，并通过一定的规则去生成MSOP、DIFOP：假设获取到的IP为`A.B.C.D`，则对应的MSOP为`C*256+D`，DIFOP为`MSOP + 1`，IP池中已经做了一些防呆，保证A=192，B=168，C≠1-5，能保证所有MSOP和DIFOP的合理性
2. 如果在`WidgetLogSetting::finishProcess`的参数中传入解绑IP池，则会将IP归还给IP池，此时需要升级AUTOSAR或者恢复雷达IP、DIFOP、MSOP和MAC地址

### 2.3 一拖多情况下IP控件适配无IP池情况

在无IP池时，可以调用`setIPandPortAccording2Index`将IP和端口号设置成跟index相关，此时第1个IP为*************，第2个为*************

当软件从一拖多退回到一拖一时，可以调用`resetIPandPort`，此时会恢复成*************

默认情况下所有index都是*************且正常析构的情况下都支持掉电保存

## 3 子组件唯一码和子组件SN的使用

WidgetLidarInfo 支持多个子组件的唯一码（SubUnicode）和子组件SN（SubSN）信息输入, 每个子组件都可以单独录入其唯一码和SN

### 3.1 添加和布局子组件输入框

- 可以通过 `setSubUnicodePos` 和 `setSubSnPos` 方法为每个子组件动态添加输入框，并设置其在界面上的布局位置。
- 这两个方法的 `_name` 参数用于区分不同的子组件（如“子件1”、“子件2”等），每个 `_name` 会对应一组输入框。
- 例如：

    ```cpp
    widget_lidar_info->setSubUnicodePos("子件1", 3, 0, 1, 1, true);
    widget_lidar_info->setSubSnPos("子件1", 3, 1, 1, 1, true);
    widget_lidar_info->setSubUnicodePos("子件2", 3, 0, 1, 1, true);
    widget_lidar_info->setSubSnPos("子件2", 3, 1, 1, 1, true);
    ```

### 3.2 数据录入与获取

- 子组件的唯一码和SN可以通过扫码枪或手动输入。
- 同時也可以通过调用 `setSubUnicode` 和 `setSubSn` 方法来设置子组件的唯一码和SN。
- 例如：

    ```cpp
    widget_lidar_info->setSubUnicode("子件1", "1234567890");
    widget_lidar_info->setSubSn("子件1", "SN001");
    ```

### 3.3 与MES/服务器的数据交互

- 在上传数据到MES或服务器时，WidgetLogSetting 会自动收集所有子组件的唯一码和SN，并以 JSON 数组的形式打包上传，无需手动拼接。
- 例如上传数据格式如下：

    ```json
    "SubSNList": [
      {"SubUnicode": "1234567890", "SubSN": "SN001"},
      {"SubUnicode": "0987654321", "SubSN": "SN002"}
    ]
    ```

### 3.4 注意事项

- 子组件输入框数量和名称可根据实际需求动态增减，建议在初始化时统一设置。
- 若某个子组件没有SN，可以留空，但是唯一码必须输入, mes给出的说法是唯一码没输入值会返回错误。
- 子组件输入框无字符限制，适应不同厂家和不同格式的编码需求。

通过上述方式，可以灵活管理和追溯每个子组件的唯一码和SN，满足复杂装配和质量追溯需求。

## 4 后续的一些扩展计划

1. 增加相机扫码功能，接收到相应触发后，自动调用相机获取SN，并完成后续流程