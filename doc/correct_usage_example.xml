﻿<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="CorrectUsageExample">
    <Sequence>
      <!-- 正确用法：设置黑板变量 -->
      <!-- output_key应该是变量名（不带花括号） -->
      <SetBlackboard output_key="voltage_value" value="4.5" />
      <SetBlackboard output_key="current_value" value="2.3" />
      <SetBlackboard output_key="register_value" value="165" />
      <SetBlackboard output_key="firmware_version" value="v1.2.3" />
      
      <!-- 使用变量进行数值测量 -->
      <!-- variable_name应该是变量名（不带花括号） -->
      <AddNumericMeasure
          label="电压测量"
          lower_limit="3.0"
          upper_limit="5.0"
          unit="V"
          variable_name="voltage_value"
          data_type="0"
      />
      
      <!-- 使用变量进行字符串测量 -->
      <AddStringMeasure
          label="固件版本检查"
          expected_value="v1.2.3"
          variable_name="firmware_version"
      />
      
      <!-- 删除不需要的变量 -->
      <UnsetBlackboard key="current_value" />
      
      <!-- 也可以直接提供值（传统方式） -->
      <AddNumericMeasure
          label="直接数值测试"
          lower_limit="1.0"
          upper_limit="3.0"
          unit="A"
          data_value="2.3"
          data_type="0"
      />
    </Sequence>
  </BehaviorTree>
</root>
