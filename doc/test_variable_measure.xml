﻿<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="TestVariableMeasure">
    <Sequence>
      <!-- 设置一些测试变量到黑板 -->
      <SetBlackboard output_key="voltage_value" value="4.5" />
      <SetBlackboard output_key="current_value" value="2.3" />
      <SetBlackboard output_key="register_value" value="165" />
      <SetBlackboard output_key="firmware_version" value="v1.2.3" />
      <SetBlackboard output_key="device_id" value="DEV001" />

      <!-- 测试从变量获取浮点数值 -->
      <AddNumericMeasure
          label="电压测量"
          lower_limit="3.0"
          upper_limit="5.0"
          unit="V"
          variable_name="voltage_value"
          data_type="0"
      />

      <!-- 测试从变量获取整数值 -->
      <AddNumericMeasure
          label="寄存器值"
          lower_limit="0"
          upper_limit="255"
          unit="count"
          variable_name="register_value"
          data_type="2"
      />

      <!-- 测试从变量获取字符串值 -->
      <AddStringMeasure
          label="固件版本"
          expected_value="v1.2.3"
          variable_name="firmware_version"
      />

      <!-- 测试直接提供数值（传统方式） -->
      <AddNumericMeasure
          label="直接数值测试"
          lower_limit="1.0"
          upper_limit="3.0"
          unit="A"
          data_value="2.3"
          data_type="0"
      />

      <!-- 测试直接提供字符串值（传统方式） -->
      <AddStringMeasure
          label="直接字符串测试"
          expected_value="DEV001"
          actual_value="DEV001"
      />

      <!-- 测试删除黑板变量 -->
      <UnsetBlackboard key="device_id" />
    </Sequence>
  </BehaviorTree>
</root>
