﻿# 🧠 BehaviorTree.CPP v4.x
## `convertFromString`

---

## 1. 基础定义
```cpp
template <typename T>
[[nodiscard]] T BT::convertFromString(BT::StringView str);
```
- 作用：把字符串 `str` 转成 `T` 类型  
- 出错会抛出 `BT::RuntimeError`

---

## 2. 内置支持的类型

| 类型         | 说明                               | 示例 |
|:-------------|:------------------------------------|:-----|
| int, int32_t, uint32_t, int64_t, uint64_t | 十进制 / 十六进制(`0x`) 支持 | `"42"`, `"0x2A"` |
| float, double | 支持小数、科学记数法 | `"3.14"`, `"1.5e-3"` |
| bool | `"true"`, `"false"`, `"1"`, `"0"` 不区分大小写 | `"True"`, `"0"` |
| std::string | 原样返回 | `"hello"` |
| std::vector<T> | 用`,`逗号分隔 | `"1,2,3"` |
| std::array<T,N> | 固定N个元素，用`,`分隔 | `"1,2,3,4"` |
| std::pair<T1,T2> | 两个元素，用`,`分隔 | `"key,value"` |

---

## 3. 自定义类型扩展

只需写模板特化，放在 `namespace BT` 下：

```cpp
namespace BT
{
template <>
inline MyType convertFromString<MyType>(StringView str)
{
    auto parts = BT::splitString(str, ',');
    // 解析parts，生成MyType对象
}
}
```

---

## 4. 使用示例

### 4.1. 获取输入
```cpp
auto val = getInput<int>("port_name"); // 自动用 convertFromString<int>
```

### 4.2. SetBlackboard 配合
```xml
<SetBlackboard output_key="threshold" value="0x10"/>
```
自动识别十六进制为16！

### 4.3. 处理 vector
```xml
<SetBlackboard output_key="vec" value="1,2,3,4"/>
```
→ 自动转成 `std::vector<int>`

---

## 5. 注意事项

- 🔥 **bool 支持** `"true"/"false"`也支持 `"1"/"0"`  
- 🔥 **vector/array/pair 用英文逗号 `,` 分隔**
- 🔥 **十六进制需加 `0x`前缀**
- ❗ 出错会抛出异常，需要 catch 或保证格式正确
- ❗ `std::vector<float>` 不支持空格分隔，必须是`1.1,2.2,3.3`

---

## 6. 典型自定义例子

### 自定义 enum class 支持
```cpp
enum class DeviceState { Idle, Running, Error };

namespace BT
{
template <>
inline DeviceState convertFromString<DeviceState>(StringView str)
{
    static const std::unordered_map<std::string, DeviceState> mapping = {
        {"Idle", DeviceState::Idle},
        {"Running", DeviceState::Running},
        {"Error", DeviceState::Error}
    };

    auto it = mapping.find(std::string(str));
    if (it == mapping.end())
    {
        throw RuntimeError("invalid DeviceState:", str);
    }
    return it->second;
}
}
```

自定义需写在`namespace BT`里面

✅ 以后直接在 XML 里写：
```xml
<SetBlackboard output_key="state" value="Running"/>
```

---

# 🚀 总结：

> **任何从 XML 来的字符串 → C++强类型变量，都靠 `convertFromString<T>` 自动搞定！**