﻿# BehaviorTree.CPP v4 基本节点详解

本文档包括 BehaviorTree.CPP v4 中的基础节点类型详细说明，分为:

- ✅ **Actions** (动作节点)
- 🔁 **Controls** (控制节点)
- 🌀 **Decorators** (装饰节点)

---

## 📁 1. Actions (动作节点)
通常是运行组件的基础操作，是行为树的可执行部分。

| 节点名 | 描述 | 返回值 |
|----------|------|---------|
| `AlwaysSuccess` | 永远返回 SUCCESS | `SUCCESS` |
| `AlwaysFailure` | 永远返回 FAILURE | `FAILURE` |
| `ForceSuccess` | 强制子节点返回 SUCCESS | `SUCCESS` |
| `ForceFailure` | 强制子节点返回 FAILURE | `FAILURE` |
| `Script` | 执行表达式，如赋值/if判断 | `SUCCESS/FAILURE` |
| `SetBlackboard` | 设置黑板变量 | `SUCCESS` |
| `GetBlackboard` | 读取黑板变量 | `SUCCESS` |
| `SubTree` / `SubTreeID` | 执行子树 | 子树返回值 |

### 示例
```xml
<Action ID="AlwaysSuccess"/>
<Script code="var1:=5"/>
<SubTree ID="PickAndPlace" var1="{target_id}"/>
```

---

## 🔁 2. Controls (控制节点)
控制子节点的执行逻辑，是行为树的结构核心。

| 节点名 | 描述 | 子节点 | 执行逻辑 |
|----------|------|--------|------------|
| `Sequence` | 顺序执行，遇到 FAILURE 结束 | ≥ 1 | 遇到失败就停止 |
| `Fallback` | 顺序试探，遇到 SUCCESS 结束 | ≥ 1 | 遇到成功就停止 |
| `Parallel` | 并行执行子节点 | ≥ 1 | 根据门格判断 |
| `ReactiveSequence` | 每 tick 重新从头追踪 | ≥ 1 | 同 Sequence |
| `ReactiveFallback` | 每 tick 重新追踪直至成功 | ≥ 1 | 同 Fallback |
| `Switch` | 按照参数值切换执行子节点 | ≥ 1 | 需自定义分支 |

### 示例
```xml
<Sequence>
    <Action ID="ScanArea"/>
    <Fallback>
        <Condition ID="IsTargetVisible"/>
        <Action ID="Rotate"/>
    </Fallback>
</Sequence>
```

---

## 🌀 3. Decorators (装饰节点)
改变或控制唯一子节点的执行逻辑。

| 节点名 | 描述 | 子节点 | 参数 |
|----------|------|--------|------|
| `Inverter` | 反转子节点结果 | 1 | 无 |
| `RetryUntilSuccessful` | 重试直至成功 | 1 | `num_attempts` |
| `Repeat` | 重复执行 N 次 | 1 | `num_cycles` |
| `Timeout` | 子节点超时则 FAILURE | 1 | `msec` |
| `Delay` | 延时执行 | 1 | `msec` |
| `ForceSuccess` | 强制 SUCCESS | 1 | 无 |
| `ForceFailure` | 强制 FAILURE | 1 | 无 |
| `BlackboardCheck` | 判断黑板 key 是否符合 | 1 | `key`, `expected` |
| `RateController` | 控制执行频率 | 1 | `hz` |
| `Throttle` | 设置最小间隔时间 | 1 | `interval` |
| `RepeatUntilFailure` | 重复直到失败 | 1 | 无 |

### 示例
```xml
<Decorator ID="Inverter">
    <Condition ID="IsDoorOpen"/>
</Decorator>

<Decorator ID="RetryUntilSuccessful" num_attempts="3">
    <Action ID="GrabObject"/>
</Decorator>

<Decorator ID="Timeout" msec="1000">
    <Action ID="PickUp"/>
</Decorator>
```

---

## 🧬 注意

- 所有 `Decorator`：只能有 **1 个子节点**
- `Condition` 是叶子节点，**不能有子节点**
- `SubTree` 允许通过 `{}` 传递参数 (input/output)
- 自定义节点：继承 BT::SyncActionNode / BT::DecoratorNode etc

---

## 📃 源码文件路径

| 类型 | 夹路径 |
|------|-----------|
| Actions | `behaviortree_cpp/actions/` |
| Controls | `behaviortree_cpp/control/` |
| Decorators | `behaviortree_cpp/decorators/` |
| Core | `behaviortree_cpp/tree_*.h` |

---

如果你需要:
- 行为树模板
- XML 实战示例
- 自定义节点 C++ 编写样例

只需一句话，我都可以给你生成哦！🚀

