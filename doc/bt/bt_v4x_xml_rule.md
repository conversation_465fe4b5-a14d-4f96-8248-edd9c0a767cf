﻿## **BehaviorTree.CPP 4.x XML 语法规则**

### **1. 根节点（`<root>`）**
根节点必须包含 `BTCPP_format="4"` 属性，表示使用 **4.x** 格式。

```xml
<root BTCPP_format="4" main_tree_to_execute="MainTree">
    <BehaviorTree ID="MainTree">
        <!-- 控制节点或动作节点等 -->
    </BehaviorTree>
</root>
```

- **`BTCPP_format="4"`**：必须指定，表示该文件符合 4.x 格式。
- **`main_tree_to_execute="MainTree"`**：指定执行的主树。

---

### **2. 行为树（`<BehaviorTree>`）**
行为树通常有一个 `ID` 属性，用于标识该行为树。

```xml
<BehaviorTree ID="MainTree">
    <Sequence>
        <Action name="MoveTo"/>
    </Sequence>
</BehaviorTree>
```

- **`ID="MainTree"`**：行为树的标识符。行为树的 `ID` 是可选的，但如果你有多个树或需要引用某个树时，这个 `ID` 非常有用。

---

### **3. 控制节点（`Sequence`, `Selector`, `Fallback`）**
控制节点是行为树的基本构建模块。它们用于控制节点的执行顺序。

```xml
<Sequence>
    <Action name="MoveTo"/>
    <Action name="Pickup"/>
</Sequence>
```

- **`Sequence`**：顺序节点，执行时按顺序依次执行子节点，直到遇到失败或所有子节点成功。
- **`Selector`**：选择节点，按照顺序依次执行子节点，只要一个子节点成功，整个选择节点就算成功。
- **`Fallback`**：后备节点，按顺序执行子节点，直到有一个子节点成功。

这些节点通常 **不能有 `ID`**，但是 **可以有 `name`**。

---

### **4. 装饰节点（`Decorator`）**
装饰节点用于修饰其他节点，通常用于增加某些条件判断或行为。

```xml
<Decorator name="RepeatUntilSuccess">
    <Action name="MoveTo"/>
</Decorator>
```

- **`name="RepeatUntilSuccess"`**：为装饰节点指定名称，装饰节点通常用于修改其子节点的执行逻辑，如重复执行、成功/失败重试等。
- **`ID` 不允许在装饰节点中使用**。

---

### **5. 动作节点（`Action`）**
动作节点用于执行实际的行为，比如移动、拾取等。

```xml
<Action name="MoveTo"/>
```

- **`ID="MoveTo"`**：标识该动作节点的标识。
- **`name` 是可选的**，但不强制要求。

---

### **6. 黑板变量（Blackboard）**
黑板用于存储状态或信息，供树中其他节点读取和修改。

```xml
<Blackboard>
    <Variable name="target_position" type="Vector3"/>
</Blackboard>
```

- **`<Blackboard>`**：黑板定义，可以在树中各个节点间共享数据。
- **`<Variable>`**：定义一个黑板变量，可以是任何类型的数据，如 `Vector3`、`int` 等。

---

## **节点属性总结**

| 节点类型        | 必须 `ID` | 必须 `name` | 说明                                                     |
|-----------------|-----------|-------------|--------------------------------------------------------|
| **`<root>`**    | ✅        | ❌          | 必须指定 `BTCPP_format="4"`，并指定主树 `main_tree_to_execute` |
| **`<BehaviorTree>`** | ✅        | ❌          | 行为树的标识符 `ID` 是可选的，方便区分多个行为树         |
| **`<Sequence>`**  | ❌        | ✅          | 控制节点，定义顺序执行，不能使用 `ID`，可以用 `name` 标识 |
| **`<Selector>`**  | ❌        | ✅          | 控制节点，定义选择执行，不能使用 `ID`，可以用 `name` 标识 |
| **`<Fallback>`**  | ❌        | ✅          | 控制节点，定义后备执行，不能使用 `ID`，可以用 `name` 标识 |
| **`<Decorator>`** | ❌        | ✅          | 装饰节点，用于修改子节点行为，不能使用 `ID`，可以用 `name` 标识 |
| **`<Action>`**    | ✅        | ❌          | 动作节点，执行实际行为，`ID` 是必需的，但 `name` 是可选的 |
| **`<Blackboard>`** | ❌        | ❌          | 定义共享变量，用于存储数据，节点间共享状态              |

---

## **补充说明**
1. **`name` 和 `ID` 的使用**：
   - **`name`**：通常用于标识和查找节点，尤其是在装饰节点和控制节点中。
   - **`ID`**：主要用于标识树本身或在需要区分多个树的情况下使用。`Action` 节点不强制要求 `ID`。

2. **装饰节点**：如 `RepeatUntilSuccess`、`AlwaysSuccess` 等，可以在装饰节点上使用 `name` 来标识，但是不能使用 `ID`。

---