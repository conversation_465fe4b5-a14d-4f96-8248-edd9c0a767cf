﻿# execAll 函数使用指南

## 简介

`execAll` 是一个强大的错误处理工具函数，它可以执行多个返回 `tl::expected` 类型的操作，并收集所有的错误信息。这个函数基于 C++17 的折叠表达式和可变参数模板，提供了一种优雅的方式来处理多个可能失败的操作。

## 功能特点

- 执行多个返回 `tl::expected` 的函数，并收集所有错误
- 所有操作都会被执行，即使前面的操作失败
- 返回一个包含所有错误信息的 `tl::expected<void, std::vector<ErrorType>>`
- 如果所有操作都成功，则返回一个成功的 `tl::expected`

## 使用方法

### 基本用法

```cpp
#include "rs_expected.h"

// 定义一些返回 tl::expected 的函数
tl::expected<int, std::string> step1() { 
    // 成功情况
    return 42; 
}

tl::expected<void, std::string> step2() { 
    // 失败情况
    return tl::unexpected("步骤2失败"); 
}

tl::expected<float, std::string> step3() { 
    // 失败情况
    return tl::unexpected("步骤3失败"); 
}

void example() {
    // 执行所有步骤并收集错误
    auto result = robosense::lidar::execAll(step1(), step2(), step3());
    
    // 检查是否有错误
    if (!result) {
        // 处理错误
        for (const auto& err : result.error()) {
            std::cout << "错误: " << err << std::endl;
        }
    } else {
        // 所有操作都成功
        std::cout << "所有步骤都成功执行!" << std::endl;
    }
}
```

### 高级用法

#### 1. 与 lambda 表达式结合使用

```cpp
auto result = robosense::lidar::execAll(
    []() -> tl::expected<int, std::string> { return 42; }(),
    []() -> tl::expected<void, std::string> { 
        if (some_condition) 
            return tl::unexpected("条件检查失败"); 
        return {}; 
    }(),
    []() -> tl::expected<float, std::string> { return 3.14f; }()
);
```

#### 2. 与条件操作结合使用

```cpp
bool should_run_step3 = true;

auto result = robosense::lidar::execAll(
    step1(),
    step2(),
    should_run_step3 ? step3() : tl::expected<float, std::string>{3.14f}
);
```

#### 3. 在类成员函数中使用

```cpp
class MyClass {
public:
    tl::expected<void, std::string> initialize() {
        return robosense::lidar::execAll(
            initializeComponent1(),
            initializeComponent2(),
            initializeComponent3()
        );
    }

private:
    tl::expected<int, std::string> initializeComponent1() { /* ... */ }
    tl::expected<void, std::string> initializeComponent2() { /* ... */ }
    tl::expected<bool, std::string> initializeComponent3() { /* ... */ }
};
```

## 注意事项

1. **错误类型必须一致**：所有传递给 `execAll` 的函数必须返回相同的错误类型。例如，如果一个函数返回 `tl::expected<int, std::string>`，那么其他函数也必须使用 `std::string` 作为错误类型。

2. **所有操作都会执行**：即使第一个操作失败，后续的操作仍然会执行。这与短路求值不同。

3. **返回值类型**：`execAll` 返回 `tl::expected<void, std::vector<ErrorType>>`，其中 `ErrorType` 是输入函数的错误类型。

4. **错误收集**：所有操作的错误都会被收集到一个 `std::vector` 中，按照操作的顺序排列。

## 实现细节

`execAll` 函数的实现位于 `include/rs_expected.h` 文件中。它使用了以下 C++17 特性：

- 折叠表达式 (fold expressions)
- 可变参数模板 (variadic templates)
- 自动类型推导 (auto return type)
- 完美转发 (perfect forwarding)

核心实现如下：

```cpp
template <typename First, typename... Rest>
auto execAll(First&& _first, Rest&&... _rest)
{
  using ErrorType = typename expected_error_type<std::decay_t<First>>::type;

  static_assert((... && std::is_same_v<ErrorType, typename expected_error_type<std::decay_t<Rest>>::type>),
                "All expects must have the same error_type!");

  std::vector<ErrorType> errors;

  auto collect = [&](auto&& _exp) {
    if (!_exp)
    {
      errors.push_back(_exp.error());
    }
  };

  collect(std::forward<First>(_first));
  (collect(std::forward<Rest>(_rest)), ...);

  using ResultType = tl::expected<void, std::vector<ErrorType>>;
  
  if (!errors.empty())
  {
    return ResultType(tl::unexpect, errors);
  }
  return ResultType{};
}
```

## 示例场景

### 1. 初始化多个组件

```cpp
tl::expected<void, std::string> initializeSystem() {
    return robosense::lidar::execAll(
        initializeDatabase(),
        initializeNetwork(),
        initializeUI(),
        loadConfiguration()
    );
}
```

### 2. 验证用户输入

```cpp
tl::expected<void, std::string> validateUserInput(const UserInput& input) {
    return robosense::lidar::execAll(
        validateUsername(input.username),
        validatePassword(input.password),
        validateEmail(input.email),
        validateAge(input.age)
    );
}
```

### 3. 执行事务操作

```cpp
tl::expected<void, std::string> performTransaction(const Transaction& tx) {
    return robosense::lidar::execAll(
        checkBalance(tx.amount),
        updateSenderAccount(tx.sender, -tx.amount),
        updateReceiverAccount(tx.receiver, tx.amount),
        logTransaction(tx)
    );
}
```

## 总结

`execAll` 函数提供了一种简洁、优雅的方式来处理多个可能失败的操作，并收集所有的错误信息。它特别适用于初始化、验证和需要执行多个步骤的场景，可以大大简化错误处理逻辑。

通过使用 `execAll`，您可以避免编写大量的嵌套条件语句，使代码更加清晰、可维护。
