﻿# PropertyView 模型/视图实现

本项目为 PropertyView 和 PropertyModel 类实现了适当的模型/视图（Model/View）设计模式。

## 所做的更改

1. **PropertyModel 更改**：
   - 修改 `PropertyModel` 继承自 QObject
   - 添加了属性值变化和模型结构变化的信号
   - 在属性注册、注销或值变化时添加了信号发射

2. **PropertyView 更改**：
   - 将 `PropertyView` 改为使用指向 `PropertyModel` 的指针，而不是嵌入它
   - 在 `PropertyView` 中添加了设置/获取模型的方法
   - 将模型的信号连接到编辑器的槽
   - 更新了 `PropertyView` 中的所有方法以使用模型指针
   - 在所有方法中添加了模型指针的空检查
   - 在 `PropertyView` 构造函数中创建了默认模型

## 优势

这些更改允许：

1. `PropertyModel` 可以独立运行，不需要 `PropertyView`
2. 多个 `PropertyView` 实例可以共享同一个 `PropertyModel`
3. 模型中的更改会自动更新所有连接的编辑器
4. 一个编辑器中的更改会更新模型，然后更新所有其他编辑器

## 使用示例

```cpp
// 创建模型
auto model = new PropertyModel();

// 在模型中注册属性
model->registerProperty<IntItem>("分组1", "整数属性", 10, 0, 100, 1);
model->registerProperty<StringItem>("分组1", "字符串属性", "默认值");

// 创建共享同一模型的多个编辑器
auto editor1 = new PropertyView();
editor1->setModel(model);

auto editor2 = new PropertyView();
editor2->setModel(model);

// 一个编辑器中的更改将反映在另一个编辑器中
// 直接对模型的更改将更新两个编辑器
```
