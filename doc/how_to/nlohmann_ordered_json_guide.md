﻿# 📦 nlohmann::ordered_json 快速上手与高效使用指南

`nlohmann::ordered_json` 是 `nlohmann::json` 的一个别名，区别在于它使用了 **有序映射容器**，可以保持字段插入顺序，适合用于配置文件、diff 比较、格式化输出等需要稳定顺序的场景。

---

## ✨ 1. 引入头文件

```cpp
#include <nlohmann/json.hpp>

using ordered_json = nlohmann::ordered_json;
```

无需单独引入任何其他头文件，`ordered_json` 是 `nlohmann::json` 内部提供的别名。

---

## 🧱 2. 创建和插入（保持顺序）

```cpp
ordered_json j;
j["z"] = 1;
j["a"] = 2;
j["m"] = 3;

std::cout << j.dump(4) << std::endl;
```

🔽 输出：

```json
{
    "z": 1,
    "a": 2,
    "m": 3
}
```

顺序与插入保持一致。

---

## 🔄 3. 序列化与反序列化

```cpp
// 序列化为字符串
std::string s = j.dump(2);

// 反序列化
ordered_json j2 = ordered_json::parse(s);
```

支持所有 JSON 类型：`object`, `array`, `string`, `number`, `boolean`, `null`。

---

## ⚙️ 4. 宏快速映射结构体

### 定义结构体

```cpp
struct Person {
    std::string name;
    int age;
};
```

### 使用宏（结构体可修改时）

```cpp
#include <nlohmann/json.hpp>
NLOHMANN_DEFINE_TYPE_INTRUSIVE(Person, name, age)
```

或（结构体不可修改时）：

```cpp
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Person, name, age)
```

### 使用：

```cpp
Person p{"Alice", 30};
ordered_json j = p;

std::cout << j.dump(4);

Person p2 = j.get<Person>();
```

---

## 🧭 5. 常用操作示例

### 遍历对象

```cpp
for (auto& [key, val] : j.items()) {
    std::cout << key << " = " << val << "\n";
}
```

### 判断是否包含字段

```cpp
if (j.contains("age")) {
    std::cout << j["age"] << "\n";
}
```

### 合并两个 JSON

```cpp
j.merge_patch(j2); // 合并 j2 到 j（覆盖模式）
```

---

## 🧠 6. ordered_json vs json

| 特性           | `json` (默认)      | `ordered_json`          |
|----------------|--------------------|--------------------------|
| 插入顺序       | ❌ 不保证           | ✅ 保证                   |
| 查找效率       | 高（map）           | 中（vector + pair）       |
| 场景推荐       | 一般数据交换       | 配置文件、调试输出等       |

---

## 📝 7. 注意事项

- `ordered_json` 的性能略低于默认 `json`，但换来了顺序稳定性。
- 插入顺序对比较和输出结果十分关键时，**强烈建议使用 `ordered_json` 替代默认 json**。
- 所有默认 `json` 用法对 `ordered_json` 均适用。

---

## 🔚 总结

`nlohmann::ordered_json` 是一个在保持接口兼容性的同时，提供字段顺序控制的强大工具。对配置、调试、序列化输出顺序有要求的项目，应该首选它而不是默认的 `json` 类型。

---

## 📚 参考

- [nlohmann/json GitHub](https://github.com/nlohmann/json)
- [C++ Reference for ordered_json](https://json.nlohmann.me/api/basic_json/)