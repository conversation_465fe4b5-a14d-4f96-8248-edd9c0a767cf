﻿# TSL 库使用指南

## 简介

TSL (Tessil) 库是一个高性能的 C++ 哈希表和集合库，提供了有序的哈希映射和集合实现。本指南主要介绍 `tsl::ordered_map` 和 `tsl::ordered_set` 的基本用法，这两个容器保持了元素插入的顺序，同时提供了接近 O(1) 的查找、插入和删除操作。

## 主要组件

TSL 库包含以下主要组件：

1. **ordered_map**: 保持插入顺序的哈希映射
2. **ordered_set**: 保持插入顺序的哈希集合
3. **ordered_hash**: 底层哈希表实现

## 安装和包含

TSL 库是一个仅头文件的库，只需要包含相应的头文件即可使用：

```cpp
#include <tsl/ordered_map.h>
#include <tsl/ordered_set.h>
```

在我们的项目中，这些头文件位于 `include/3rd_party/tsl/` 目录下。

## tsl::ordered_map 基本用法

### 创建和初始化

```cpp
#include <tsl/ordered_map.h>
#include <string>

// 创建一个空的 ordered_map
tsl::ordered_map<std::string, int> map1;

// 使用初始化列表创建
tsl::ordered_map<std::string, int> map2 = {
    {"one", 1},
    {"two", 2},
    {"three", 3}
};

// 指定初始桶数量
tsl::ordered_map<std::string, int> map3(100);
```

### 插入和访问元素

```cpp
// 插入元素
map1.insert({"four", 4});
map1.emplace("five", 5);

// 使用 [] 操作符插入或修改元素
map1["six"] = 6;
map1["one"] = 10;  // 修改已存在的元素

// 使用 at() 访问元素（如果键不存在会抛出异常）
int value = map1.at("five");  // 返回 5

// 使用 [] 操作符访问元素（如果键不存在会创建新元素）
int another_value = map1["six"];  // 返回 6

// 检查键是否存在
bool exists = map1.count("seven") > 0;  // 返回 false
```

### 迭代和遍历

```cpp
// 按插入顺序遍历所有元素
for (const auto& pair : map1) {
    std::cout << pair.first << ": " << pair.second << std::endl;
}

// 使用迭代器
for (auto it = map1.begin(); it != map1.end(); ++it) {
    std::cout << it->first << ": " << it->second << std::endl;
}

// 反向遍历
for (auto it = map1.rbegin(); it != map1.rend(); ++it) {
    std::cout << it->first << ": " << it->second << std::endl;
}
```

### 删除元素

```cpp
// 通过键删除元素
map1.erase("one");

// 通过迭代器删除元素
auto it = map1.find("two");
if (it != map1.end()) {
    map1.erase(it);
}

// 清空映射
map1.clear();
```

## tsl::ordered_set 基本用法

### 创建和初始化

```cpp
#include <tsl/ordered_set.h>
#include <string>

// 创建一个空的 ordered_set
tsl::ordered_set<std::string> set1;

// 使用初始化列表创建
tsl::ordered_set<std::string> set2 = {"apple", "banana", "cherry"};

// 指定初始桶数量
tsl::ordered_set<std::string> set3(100);
```

### 插入和检查元素

```cpp
// 插入元素
set1.insert("orange");
set1.emplace("grape");

// 检查元素是否存在
bool has_apple = set1.count("apple") > 0;  // 返回 false
bool has_orange = set1.count("orange") > 0;  // 返回 true

// 查找元素
auto it = set1.find("grape");
if (it != set1.end()) {
    std::cout << "Found: " << *it << std::endl;
}
```

### 迭代和遍历

```cpp
// 按插入顺序遍历所有元素
for (const auto& value : set1) {
    std::cout << value << std::endl;
}

// 使用迭代器
for (auto it = set1.begin(); it != set1.end(); ++it) {
    std::cout << *it << std::endl;
}
```

### 删除元素

```cpp
// 通过值删除元素
set1.erase("orange");

// 通过迭代器删除元素
auto it = set1.find("grape");
if (it != set1.end()) {
    set1.erase(it);
}

// 清空集合
set1.clear();
```

## 高级用法

### 自定义哈希函数

```cpp
#include <tsl/ordered_map.h>

struct Point {
    int x;
    int y;
    
    bool operator==(const Point& other) const {
        return x == other.x && y == other.y;
    }
};

struct PointHash {
    std::size_t operator()(const Point& p) const {
        return std::hash<int>()(p.x) ^ (std::hash<int>()(p.y) << 1);
    }
};

// 使用自定义类型和哈希函数
tsl::ordered_map<Point, std::string, PointHash> point_map;
point_map.insert({{1, 2}, "Point(1,2)"});
```

### 使用 insert_or_assign 和 try_emplace

```cpp
// 如果键不存在则插入，否则更新值
map1.insert_or_assign("key", "new_value");

// 如果键不存在则原地构造，否则不做任何操作
map1.try_emplace("key", "value");
```

### 使用预计算的哈希值

```cpp
std::string key = "example";
std::size_t hash = std::hash<std::string>()(key);

// 使用预计算的哈希值加速查找
if (map1.count(key, hash) > 0) {
    int value = map1.at(key, hash);
}
```

## 性能考虑

1. **初始容量**: 如果预先知道大致的元素数量，可以通过构造函数指定初始桶数量，减少重新哈希的次数。

2. **负载因子**: 可以通过 `max_load_factor()` 方法调整负载因子，影响哈希表的性能和内存使用。

3. **预留空间**: 使用 `reserve()` 方法预留空间，避免频繁的内存分配。

```cpp
// 预留空间给 1000 个元素
map1.reserve(1000);
```

## 与标准库容器的区别

与 `std::unordered_map` 和 `std::unordered_set` 相比，TSL 库的 `ordered_map` 和 `ordered_set` 有以下主要区别：

1. **保持插入顺序**: 元素按照插入的顺序存储和迭代，而不是按照哈希值。

2. **性能**: 在大多数情况下，TSL 库的实现比标准库更快，特别是在插入和删除操作上。

3. **内存使用**: TSL 库通常比标准库使用更少的内存。

4. **API 扩展**: 提供了一些标准库没有的方法，如 `insert_or_assign`、`try_emplace` 等。

## 实际应用示例

### 示例 1: 计数器

```cpp
#include <tsl/ordered_map.h>
#include <string>
#include <iostream>

void count_words(const std::vector<std::string>& words) {
    tsl::ordered_map<std::string, int> counter;
    
    for (const auto& word : words) {
        counter[word]++;
    }
    
    // 按照单词首次出现的顺序打印统计结果
    for (const auto& pair : counter) {
        std::cout << pair.first << ": " << pair.second << std::endl;
    }
}
```

### 示例 2: 属性系统

```cpp
#include <tsl/ordered_map.h>
#include <string>
#include <variant>

class PropertySystem {
private:
    using PropertyValue = std::variant<int, float, std::string, bool>;
    tsl::ordered_map<std::string, PropertyValue> properties;
    
public:
    template<typename T>
    void setProperty(const std::string& name, const T& value) {
        properties[name] = value;
    }
    
    template<typename T>
    T getProperty(const std::string& name, const T& default_value) const {
        auto it = properties.find(name);
        if (it != properties.end()) {
            try {
                return std::get<T>(it->second);
            } catch (const std::bad_variant_access&) {
                return default_value;
            }
        }
        return default_value;
    }
    
    bool hasProperty(const std::string& name) const {
        return properties.count(name) > 0;
    }
    
    // 按照属性添加的顺序遍历所有属性
    void printProperties() const {
        for (const auto& pair : properties) {
            std::cout << pair.first << ": ";
            std::visit([](const auto& value) { std::cout << value; }, pair.second);
            std::cout << std::endl;
        }
    }
};
```

## 总结

TSL 库提供了高性能的有序哈希映射和集合实现，它们保持了元素插入的顺序，同时提供了接近 O(1) 的查找、插入和删除操作。这些容器特别适用于需要记住元素插入顺序的场景，如配置系统、属性系统、JSON 解析等。

与标准库的无序容器相比，TSL 库的实现通常更快、更节省内存，并提供了更丰富的 API。
