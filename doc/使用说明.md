﻿# SetBlackboard和变量测量节点使用说明

## 问题分析

从您提供的日志可以看出，出现了以下错误：

```xml
XML 解析出错: missing port [output_key]
```

这是因为在SetBlackboard节点的`output_key`字段中输入了`{value}`，这在BehaviorTree中被解释为"从黑板获取名为'value'的变量值"，但此时黑板中还没有这个变量，所以出现了错误。

## 正确使用方法

### 1. SetBlackboard节点的正确用法

**错误用法：**

```c
<SetBlackboard output_key="{value}" value="-4.5" />
```

**正确用法：**

```xml
<SetBlackboard output_key="value" value="-4.5" />
```

**说明：**

- `output_key`字段应该填写**变量名**（不带花括号）
- `value`字段填写要存储的值
- 这样会在黑板中创建一个名为"value"的变量，值为"-4.5"

### 2. AddNumericMeasure节点使用变量的正确用法

**正确用法：**

```xml
<AddNumericMeasure
    label="测量项"
    lower_limit="0"
    upper_limit="100"
    unit="mm"
    variable_name="value"
    data_type="0"
/>
```

**说明：**

- `variable_name`字段应该填写**变量名**（不带花括号）
- 节点会自动从黑板获取名为"value"的变量值进行测量

### 3. 在UI中的正确操作步骤

1. **设置黑板变量：**
   - 拖拽"设置黑板变量 (SetBlackboard)"节点到画布
   - 双击节点打开属性面板
   - 在"值"字段输入：`-4.5`
   - 在"输出键"字段输入：`value`（不要加花括号）

2. **使用变量进行测量：**
   - 拖拽"添加数值测量"节点到画布
   - 双击节点打开属性面板
   - 在"变量名"字段输入：`value`（不要加花括号）
   - 设置其他测量参数（标签、上下限、单位等）

### 4. 完整的工作流程示例

```xml
<Sequence>
  <!-- 步骤1：设置变量 -->
  <SetBlackboard output_key="voltage_value" value="4.5" />
  <SetBlackboard output_key="current_value" value="2.3" />
  
  <!-- 步骤2：使用变量进行测量 -->
  <AddNumericMeasure
      label="电压测量"
      lower_limit="3.0"
      upper_limit="5.0"
      unit="V"
      variable_name="voltage_value"
      data_type="0"
  />
  
  <AddNumericMeasure
      label="电流测量"
      lower_limit="1.0"
      upper_limit="3.0"
      unit="A"
      variable_name="current_value"
      data_type="0"
  />
  
  <!-- 步骤3：清理不需要的变量 -->
  <UnsetBlackboard key="voltage_value" />
</Sequence>
```

### 5. 数据类型说明

在AddNumericMeasure节点中，`data_type`字段支持以下值：

- `0` = 浮点数 (MEASURE_DATA_TYPE_FLOAT)
- `1` = 十六进制 (MEASURE_DATA_TYPE_HEX)
- `2` = 整数 (MEASURE_DATA_TYPE_INT)

### 6. 兼容性说明

修改后的节点完全向后兼容：

- 如果提供了`variable_name`，优先使用变量值
- 如果没有提供`variable_name`，使用`data_value`或`actual_value`字段的值
- 这样既支持新的变量功能，又保持了原有的直接输入值的功能

### 7. 常见错误和解决方法

**错误1：** `missing port [output_key]`

- **原因：** 在`output_key`字段中使用了`{变量名}`格式
- **解决：** 直接输入变量名，不要加花括号

**错误2：** `Failed to get variable 'xxx' from blackboard`

- **原因：** 在使用变量之前没有先设置变量
- **解决：** 确保在使用变量之前先用SetBlackboard节点设置变量

**错误3：** 测量值不正确

- **原因：** 变量名拼写错误或大小写不匹配
- **解决：** 检查变量名的拼写和大小写是否一致
