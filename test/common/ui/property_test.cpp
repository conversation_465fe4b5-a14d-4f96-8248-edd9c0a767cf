﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "common/property/property_view.h"
#include <QApplication>
#include <gtest/gtest.h>
#include <memory>

// 需要一个QApplication实例来测试Qt组件
class PropertyTest : public ::testing::Test
{
protected:
  static QApplication* app_;

  static void SetUpTestSuite()
  {
    // 创建QApplication实例，用于测试Qt组件
    static int argc     = 1;
    static char* argv[] = { (char*)"property_test" };
    app_                = new QApplication(argc, argv);
  }

  static void TearDownTestSuite() { delete app_; }
};

QApplication* PropertyTest::app_ = nullptr;

// 测试IntPropertyItem类
TEST_F(PropertyTest, IntPropertyItemTest)
{
  // 创建IntPropertyItem实例
  robosense::lidar::IntItem int_item("test_int", 10, 0, 100, 1);

  // 测试createEditor方法
  QWidget* view = int_item.createEditor();
  ASSERT_NE(view, nullptr);

  // 测试默认值
  EXPECT_EQ(int_item.value().toInt(), 10);

  // 测试设置值
  int_item.setValue(QVariant(50));
  EXPECT_EQ(int_item.value().toInt(), 50);

  // 测试边界值
  int_item.setValue(QVariant(0));
  EXPECT_EQ(int_item.value().toInt(), 0);

  int_item.setValue(QVariant(100));
  EXPECT_EQ(int_item.value().toInt(), 100);

  // 测试超出范围的值（应该被限制在范围内）
  int_item.setValue(QVariant(-10));
  EXPECT_GE(int_item.value().toInt(), 0);

  int_item.setValue(QVariant(200));
  EXPECT_LE(int_item.value().toInt(), 100);
}

// 测试DoublePropertyItem类
TEST_F(PropertyTest, DoublePropertyItemTest)
{
  // 创建DoublePropertyItem实例
  robosense::lidar::DoubleItem double_item("test_double", 5.5, 0.0, 10.0, 0.1, 2);

  // 测试createEditor方法
  QWidget* view = double_item.createEditor();
  ASSERT_NE(view, nullptr);

  // 测试默认值
  EXPECT_DOUBLE_EQ(double_item.value().toDouble(), 5.5);

  // 测试设置值
  double_item.setValue(QVariant(7.5));
  EXPECT_DOUBLE_EQ(double_item.value().toDouble(), 7.5);

  // 测试边界值
  double_item.setValue(QVariant(0.0));
  EXPECT_DOUBLE_EQ(double_item.value().toDouble(), 0.0);

  double_item.setValue(QVariant(10.0));
  EXPECT_DOUBLE_EQ(double_item.value().toDouble(), 10.0);

  // 测试超出范围的值（应该被限制在范围内）
  double_item.setValue(QVariant(-5.0));
  EXPECT_GE(double_item.value().toDouble(), 0.0);

  double_item.setValue(QVariant(15.0));
  EXPECT_LE(double_item.value().toDouble(), 10.0);
}

// 测试StringPropertyItem类
TEST_F(PropertyTest, StringPropertyItemTest)
{
  // 创建StringPropertyItem实例
  robosense::lidar::StringItem string_item("test_string", "default_value");

  // 测试createEditor方法
  QWidget* view = string_item.createEditor();
  ASSERT_NE(view, nullptr);

  // 测试默认值
  EXPECT_EQ(string_item.value().toString().toStdString(), "default_value");

  // 测试设置值
  string_item.setValue(QVariant(QString("new_value")));
  EXPECT_EQ(string_item.value().toString().toStdString(), "new_value");

  // 测试空字符串
  string_item.setValue(QVariant(QString("")));
  EXPECT_EQ(string_item.value().toString().toStdString(), "");
}

// 测试BoolPropertyItem类
TEST_F(PropertyTest, BoolPropertyItemTest)
{
  // 创建BoolPropertyItem实例
  robosense::lidar::BoolItem bool_item("test_bool", true);

  // 测试createEditor方法
  QWidget* view = bool_item.createEditor();
  ASSERT_NE(view, nullptr);

  // 测试默认值
  EXPECT_TRUE(bool_item.value().toBool());

  // 测试设置值
  bool_item.setValue(QVariant(false));
  EXPECT_FALSE(bool_item.value().toBool());

  bool_item.setValue(QVariant(true));
  EXPECT_TRUE(bool_item.value().toBool());
}

// 测试EnumPropertyItem类
TEST_F(PropertyTest, EnumPropertyItemTest)
{
  // 创建选项列表
  QStringList options;
  options << "Option1"
          << "Option2"
          << "Option3";

  // 创建EnumPropertyItem实例
  robosense::lidar::EnumItem enum_item("test_enum", options, 1);

  // 测试createEditor方法
  QWidget* view = enum_item.createEditor();
  ASSERT_NE(view, nullptr);

  // 测试默认值
  EXPECT_EQ(enum_item.value().toInt(), 1);
  EXPECT_EQ(enum_item.currentText(), "Option2");

  // 测试设置值
  enum_item.setValue(QVariant(0));
  EXPECT_EQ(enum_item.value().toInt(), 0);
  EXPECT_EQ(enum_item.currentText(), "Option1");

  enum_item.setValue(QVariant(2));
  EXPECT_EQ(enum_item.value().toInt(), 2);
  EXPECT_EQ(enum_item.currentText(), "Option3");

  // 测试超出范围的值（应该被限制在范围内或忽略）
  enum_item.setValue(QVariant(5));
  // 注意：QComboBox会忽略无效的索引，所以这里不会改变当前值
  EXPECT_LE(enum_item.value().toInt(), options.size() - 1);
}

// 测试PropertyGroup类
TEST_F(PropertyTest, PropertyGroupTest)
{
  // 创建PropertyGroup实例
  robosense::lidar::PropertyGroup group("test_group");

  // 测试名称
  EXPECT_EQ(group.name(), "test_group");

  // 测试设置名称
  group.setName("new_group_name");
  EXPECT_EQ(group.name(), "new_group_name");

  // 测试复制构造函数
  robosense::lidar::PropertyGroup group_copy(group);
  EXPECT_EQ(group_copy.name(), "new_group_name");

  // 测试赋值操作符
  robosense::lidar::PropertyGroup group_assign("another_group");
  group_assign = group;
  EXPECT_EQ(group_assign.name(), "new_group_name");

  // 测试移动构造函数
  robosense::lidar::PropertyGroup group_move(std::move(robosense::lidar::PropertyGroup("move_group")));
  EXPECT_EQ(group_move.name(), "move_group");

  // 测试移动赋值操作符
  group_assign = std::move(robosense::lidar::PropertyGroup("moved_group"));
  EXPECT_EQ(group_assign.name(), "moved_group");
}

// 测试PropertyModel类
TEST_F(PropertyTest, PropertyModelTest)
{
  // 创建PropertyModel实例
  robosense::lidar::PropertyModel model;

  // 测试注册整数属性
  model.registerProperty<robosense::lidar::IntItem>("group1", "int_prop", 10, 0, 100, 1);

  // 获取属性并创建编辑器
  std::string int_full_key = "group1.int_prop";
  auto int_prop            = model.properties().at(int_full_key);
  int_prop->createEditor();

  EXPECT_EQ(model.getValue<int>("group1", "int_prop"), 10);

  // 测试注册浮点数属性
  model.registerProperty<robosense::lidar::DoubleItem>("group1", "double_prop", 5.5, 0.0, 10.0, 0.1, 2);

  // 获取属性并创建编辑器
  std::string double_full_key = "group1.double_prop";
  auto double_prop            = model.properties().at(double_full_key);
  double_prop->createEditor();

  EXPECT_DOUBLE_EQ(model.getValue<double>("group1", "double_prop"), 5.5);

  // 测试注册字符串属性
  model.registerProperty<robosense::lidar::StringItem>("group2", "string_prop", "test_string");

  // 获取属性并创建编辑器
  std::string string_full_key = "group2.string_prop";
  auto string_prop            = model.properties().at(string_full_key);
  string_prop->createEditor();

  EXPECT_EQ(model.getValue<QString>("group2", "string_prop").toStdString(), "test_string");

  // 测试注册布尔属性
  model.registerProperty<robosense::lidar::BoolItem>("group2", "bool_prop", true);

  // 获取属性并创建编辑器
  std::string bool_full_key = "group2.bool_prop";
  auto bool_prop            = model.properties().at(bool_full_key);
  bool_prop->createEditor();

  EXPECT_TRUE(model.getValue<bool>("group2", "bool_prop"));

  // 测试设置值
  model.setValue("group1", "int_prop", 50);
  EXPECT_EQ(model.getValue<int>("group1", "int_prop"), 50);

  model.setValue("group1", "double_prop", 7.5);
  EXPECT_DOUBLE_EQ(model.getValue<double>("group1", "double_prop"), 7.5);

  model.setValue("group2", "string_prop", QString("new_string"));
  EXPECT_EQ(model.getValue<QString>("group2", "string_prop").toStdString(), "new_string");

  model.setValue("group2", "bool_prop", false);
  EXPECT_FALSE(model.getValue<bool>("group2", "bool_prop"));

  // 测试注销属性
  EXPECT_TRUE(model.unregisterProperty("group1", "int_prop"));
  // 尝试获取已注销的属性应该返回默认值
  EXPECT_EQ(model.getValue<int>("group1", "int_prop"), 0);

  // 测试注销组
  EXPECT_TRUE(model.unregisterGroup("group2"));
  // 尝试获取已注销组中的属性应该返回默认值
  EXPECT_EQ(model.getValue<QString>("group2", "string_prop").toStdString(), "");
  EXPECT_FALSE(model.getValue<bool>("group2", "bool_prop"));
}

// 测试PropertyView类的基本功能
TEST_F(PropertyTest, PropertyViewBasicTest)
{
  // 创建PropertyView实例（现在会自动创建一个默认的PropertyModel）
  robosense::lidar::PropertyView view(nullptr, false);

  // 测试注册整数属性
  view.registerInt("test_group", "int_prop", 10, 0, 100, 1);
  // PropertyView在注册属性时会自动创建编辑器，所以不需要手动创建
  EXPECT_EQ(view.getValue<int>("test_group", "int_prop"), 10);

  // 测试注册浮点数属性
  view.registerDouble("test_group", "double_prop", 5.5, 0.0, 10.0, 0.1, 2);
  EXPECT_DOUBLE_EQ(view.getValue<double>("test_group", "double_prop"), 5.5);

  // 测试注册字符串属性
  view.registerString("test_group", "string_prop", "test_string");
  EXPECT_EQ(view.getValue<QString>("test_group", "string_prop").toStdString(), "test_string");

  // 测试注册布尔属性
  view.registerBool("test_group", "bool_prop", true);
  EXPECT_TRUE(view.getValue<bool>("test_group", "bool_prop"));

  // 测试注册枚举属性
  QStringList options;
  options << "Option1"
          << "Option2"
          << "Option3";
  view.registerEnum("test_group", "enum_prop", options, 1);
  EXPECT_EQ(view.getValue<int>("test_group", "enum_prop"), 1);

  // 测试设置值
  view.setValue("test_group", "int_prop", 50);
  EXPECT_EQ(view.getValue<int>("test_group", "int_prop"), 50);

  view.setValue("test_group", "double_prop", 7.5);
  EXPECT_DOUBLE_EQ(view.getValue<double>("test_group", "double_prop"), 7.5);

  view.setValue("test_group", "string_prop", QString("new_string"));
  EXPECT_EQ(view.getValue<QString>("test_group", "string_prop").toStdString(), "new_string");

  view.setValue("test_group", "bool_prop", false);
  EXPECT_FALSE(view.getValue<bool>("test_group", "bool_prop"));

  view.setValue("test_group", "enum_prop", 2);
  EXPECT_EQ(view.getValue<int>("test_group", "enum_prop"), 2);

  // 测试注销属性
  EXPECT_TRUE(view.unregisterProperty("test_group", "int_prop"));

  // 测试注销组
  EXPECT_TRUE(view.unregisterGroup("test_group"));
}

// 测试PropertyView和PropertyModel的关联功能
TEST_F(PropertyTest, PropertyViewModelRelationshipTest)
{
  // 创建共享的PropertyModel
  auto model = std::make_shared<robosense::lidar::PropertyModel>();

  // 在模型中注册属性
  model->registerProperty<robosense::lidar::IntItem>("group1", "int_prop", 10, 0, 100, 1);
  model->registerProperty<robosense::lidar::StringItem>("group1", "string_prop", "default_value");

  // 创建两个使用同一模型的PropertyView
  robosense::lidar::PropertyView view1(nullptr, false);
  view1.setModel(model);

  robosense::lidar::PropertyView view2(nullptr, false);
  view2.setModel(model);

  // 测试两个视图共享同一模型的数据
  EXPECT_EQ(view1.getValue<int>("group1", "int_prop"), 10);
  EXPECT_EQ(view2.getValue<int>("group1", "int_prop"), 10);

  // 通过第一个视图修改值
  view1.setValue("group1", "int_prop", 50);

  // 验证两个视图都能看到更新后的值
  EXPECT_EQ(view1.getValue<int>("group1", "int_prop"), 50);
  EXPECT_EQ(view2.getValue<int>("group1", "int_prop"), 50);

  // 通过第二个视图修改值
  view2.setValue("group1", "string_prop", QString("new_value"));

  // 验证两个视图都能看到更新后的值
  EXPECT_EQ(view1.getValue<QString>("group1", "string_prop").toStdString(), "new_value");
  EXPECT_EQ(view2.getValue<QString>("group1", "string_prop").toStdString(), "new_value");

  // 测试直接通过模型修改值
  model->setValue("group1", "int_prop", 75);

  // 验证两个视图都能看到更新后的值
  EXPECT_EQ(view1.getValue<int>("group1", "int_prop"), 75);
  EXPECT_EQ(view2.getValue<int>("group1", "int_prop"), 75);
}

// 主函数
int main(int argc, char** argv)
{
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
