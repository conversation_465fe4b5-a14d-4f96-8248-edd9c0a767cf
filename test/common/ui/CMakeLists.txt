﻿cmake_minimum_required(VERSION 3.10)

# 设置测试项目名称
set(TEST_NAME property_test)

# 查找Qt5包
find_package(
  Qt5
  COMPONENTS Core Widgets Xml Svg
  REQUIRED)

# 查找GTest包
find_package(GTest REQUIRED)

# 添加测试可执行文件
add_executable(${TEST_NAME} property_test.cpp)

# 包含头文件目录
target_include_directories(
  ${TEST_NAME} PRIVATE ${CMAKE_SOURCE_DIR} ${CMAKE_SOURCE_DIR}/src ${CMAKE_SOURCE_DIR}/include # 添加 include 目录
                       ${GTEST_INCLUDE_DIRS})

# 链接Qt5库和GTest库
target_link_libraries(
  ${TEST_NAME}
  PRIVATE Qt5::Core
          Qt5::Widgets
          Qt5::Xml
          Qt5::Svg
          ${GTEST_BOTH_LIBRARIES}
          pthread
          common::common)

# 添加测试
add_test(NAME ${TEST_NAME} COMMAND ${TEST_NAME})
