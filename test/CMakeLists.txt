﻿set(BUILD_GMOCK
    OFF
    CACHE BOOL "disable gmock build")

# 修改 MD 为 MT
if(WIN32)
  set(CMAKE_CXX_FLAGS_RELEASE "/MT")
  set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "/MT")
  set(CMAKE_CXX_FLAGS_DEBUG "/MTd")
endif(WIN32)

# If GoogleTest is not found, fetch it using FetchContent
find_package(GTest QUIET)
if(NOT GTest_FOUND)
  include(FetchContent)
  FetchContent_Declare(
    googletest
    GIT_REPOSITORY ***********************:system_codebase/factory_tool/common_lib/googletest.git
    GIT_TAG release-1.12.1
    GIT_SHALLOW TRUE
    GIT_DEPTH 1
    GIT_CONFIG advice.detachedHead=false)

  # Download and configure GoogleTest
  FetchContent_MakeAvailable(googletest)
endif()

add_executable(test_main test.cpp)

if(WIN32)
  target_link_libraries(test_main gtest)
else()
  target_link_libraries(test_main gtest pthread)
endif()

# 启用测试
enable_testing()

# 添加common子目录
add_subdirectory(common)

target_include_directories(test_main PRIVATE ${PROJECT_SOURCE_DIR}/include ${PROJECT_SOURCE_DIR}/src)
target_include_directories(test_main SYSTEM PRIVATE ${GTEST_INCLUDE_DIRS})
set_target_properties(
  test_main
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)

if(WIN32)
  target_compile_options(test_main PRIVATE /utf-8)
else()
  target_compile_options(test_main PRIVATE -fPIC -Wall -O3)
endif(WIN32)
