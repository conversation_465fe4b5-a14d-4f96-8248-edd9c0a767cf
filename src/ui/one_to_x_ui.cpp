﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "one_to_x_ui.h"
#include "config.h"
#include "include/widget_lidar_info.h"
#include "property/property_view.h"
#include "ui/app_event.h"
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

OneToXUi::OneToXUi(const int _current_index, QMainWindow* _main_win, QObject* _parent) :
  QObject(_parent), main_win_(_main_win), current_lidar_index_(_current_index)
{
  setupLayout();
}

QWidget* OneToXUi::getWidgetControl() const { return groupbox_lidar_ctrl_; }

rsfsc_lib::WidgetLidarInfo* OneToXUi::getWidgetLidarInfo() const { return widget_lidar_info_; }

void OneToXUi::setupLayout()
{
  QString device_name  = QString::fromUtf8("设备%1：").arg(QString::number(current_lidar_index_));
  groupbox_lidar_ctrl_ = new QGroupBox(device_name);
  layout_lidar_ctrl_   = new QVBoxLayout;

  layout_lidar_ctrl_->setSpacing(1);
  groupbox_lidar_ctrl_->setLayout(layout_lidar_ctrl_);

  createLidarInfo();
}

void OneToXUi::createLidarInfo()
{
  if (nullptr != widget_lidar_info_)
  {
    LOG_DEBUG("createLidarInfo : widget_lidar_info_ has beed created");
    return;
  }
  auto* win = dynamic_cast<MainWindow*>(main_win_);
  if (nullptr == win)
  {
    LOG_ERROR("createLidarInfo : dynamic_cast<MainWindow*> failed");
    return;
  }
  const auto& model = win->propertyView()->model();

  //lidar info
  widget_lidar_info_ = new rsfsc_lib::WidgetLidarInfo(QString::fromUtf8(PROJECT_NAME), current_lidar_index_, nullptr);
  widget_lidar_info_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
  //add widget lidar info's item
  auto lidar_name = fmt::format("雷达{}固定参数", current_lidar_index_);

  // 获取控件可见性配置
  bool project_code_visible   = model->getValue<bool>(lidar_name, "项目编号是否可见");
  bool lidar_position_visible = model->getValue<bool>(lidar_name, "雷达安装位置是否可见");
  bool lidar_sn_visible       = model->getValue<bool>(lidar_name, "雷达SN是否可见");
  bool lidar_ip_visible       = model->getValue<bool>(lidar_name, "雷达IP地址是否可见");
  bool msop_port_visible      = model->getValue<bool>(lidar_name, "MSOP端口号是否可见");
  bool difop_port_visible     = model->getValue<bool>(lidar_name, "DIFOP端口号是否可见");

  // 根据可见性设置布局
  int row = 0;
  int col = 0;

  // 项目编号控件 - 如果可见，放在第一行第一列
  if (project_code_visible)
  {
    widget_lidar_info_->setProjectCodePos(row, col);  //项目编号控件
    col++;                                            // 移动到下一列
  }

  // 雷达位置控件 - 如果可见，放在第一行可用的下一列，占用1行2列
  if (lidar_position_visible)
  {
    widget_lidar_info_->setLidarPositionPos(row, col, 1, 2, true);  //雷达位置控件
    // 不需要增加col，因为下一个控件会放在下一行
  }

  // 第二行：IP地址和MSOP端口
  int second_row = row + 1;
  int second_col = 0;

  // IP地址控件
  if (lidar_ip_visible)
  {
    widget_lidar_info_->setIPPos(second_row, second_col);
    second_col++;
  }

  // MSOP端口控件
  if (msop_port_visible)
  {
    widget_lidar_info_->setMSOPPos(second_row, second_col);
    second_col++;
  }

  // 第三行：DIFOP端口控件 - 跨越所有列
  if (difop_port_visible)
  {
    // 计算总列数 - 考虑第一行和第二行的最大列数
    int first_row_cols  = (project_code_visible ? 1 : 0) + (lidar_position_visible ? 2 : 0);
    int second_row_cols = (lidar_ip_visible ? 1 : 0) + (msop_port_visible ? 1 : 0);
    int total_cols      = std::max(first_row_cols, second_row_cols);
    // 确保至少有1列
    total_cols = (total_cols > 0) ? total_cols : 1;

    widget_lidar_info_->setDIFOPPos(row + 2, 0, 1, total_cols);  //DIFOP端口控件
  }

  // 第四行：雷达SN控件 - 跨越所有列
  if (lidar_sn_visible)
  {
    // 计算总列数 - 考虑所有行的最大列数
    int first_row_cols  = (project_code_visible ? 1 : 0) + (lidar_position_visible ? 2 : 0);
    int second_row_cols = (lidar_ip_visible ? 1 : 0) + (msop_port_visible ? 1 : 0);
    int total_cols      = std::max(first_row_cols, second_row_cols);
    // 确保至少有1列
    total_cols = (total_cols > 0) ? total_cols : 1;

    widget_lidar_info_->setLidarSNPos(row + 3, 0, 1, total_cols);  //扫码枪输入控件
  }

  layout_lidar_ctrl_->insertWidget(1, widget_lidar_info_);

  G_APP->getMES()->registerWidgetLidarInfo(widget_lidar_info_);

  //   check_if_auto_test();

  if (current_lidar_index_ == 1)
  {
    //使用一托一时, 进行扫码后的开始测试聚焦
    QObject::connect(widget_lidar_info_, &rsfsc_lib::WidgetLidarInfo::signalLidarNameInputFinished, this,
                     [this]() { pushbutton_test_->setFocus(); });
  }
}

}  // namespace robosense::lidar
