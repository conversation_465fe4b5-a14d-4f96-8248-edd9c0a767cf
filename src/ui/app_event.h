﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef APP_EVENT_H
#define APP_EVENT_H

#include "config.h"
#include "csv_parser.h"
#include "include/widget_log_setting.h"
#include "main_window.h"
#include "singleton.h"
#include <QtCore/QObject>
#include <string>

namespace robosense
{
namespace lidar
{
class MainWindow;

class AppEvent : public QObject, public Singleton<AppEvent>
{
  Q_OBJECT
public:
  AppEvent(const AppEvent&) = delete;
  AppEvent(AppEvent&&)      = delete;
  AppEvent& operator=(AppEvent&&) = delete;
  AppEvent& operator=(const AppEvent&) = delete;
  ~AppEvent() override                 = default;
  friend class Singleton<AppEvent>;

  MainWindow* getMainWin()
  {
    if (nullptr == main_window_)
    {
      main_window_ = std::make_unique<MainWindow>();
    }
    return main_window_.get();
  }

  void destroyMainWin()
  {
    if (nullptr != main_window_)
    {
      main_window_.reset();
    }
  }

  /**
    * @brief     get the widget log setting object as mes
    * @retval    the pointer to the widget log setting object
    **/
  robosense::lidar::rsfsc_lib::WidgetLogSetting* getMES()
  {
    if (nullptr == mes_)
    {
      mes_ = std::make_unique<robosense::lidar::rsfsc_lib::WidgetLogSetting>();
      mes_->setWindowIcon(QIcon(":/img/icon.bmp"));
    }

    return mes_.get();
  }

  /**
    * @brief     destroy the widget log setting object
    **/
  void destroyMES()
  {
    if (nullptr != mes_)
    {
      mes_.reset();
    }
  }

  CsvParser* getCsvInfo()
  {
    if (nullptr == csv_info_)
    {
      csv_info_ = std::make_unique<robosense::lidar::CsvParser>();
    }
    return csv_info_.get();
  }

  static std::string getConfigDir()
  {
#ifdef _WIN32
    std::string limit_dir = QCoreApplication::applicationDirPath().toStdString() + "/config/";
#else
    std::string limit_dir { "/home/<USER>/work/common/fixture_test/config/" };
#endif
    return limit_dir;
  }

  [[nodiscard]] int getModuleIndex() const { return current_module_idx_; }
  [[nodiscard]] int getOneToXNum() const { return current_one_to_x_num_; }

Q_SIGNALS:

  /****************************************************************
    * RULES:
    * 1. use "update" but not "change"          
   ****************************************************************/
  //  signal that all ui should connect
  void signalUpdateNameSpace(const QString&);
  void signalSaveParam();

  //  signal that all ui can connect
  void signalShowInfoText(const QString& _msg);
  void signalShowWarningText(const QString& _msg);
  void signalShowErrorText(const QString& _msg);
  void signalShowInfoVariant(const QString& _name, const QVariant& _value);

  //  signal that only for specify ui
  void signalUpdateAllWidgetState(const std::size_t _lidar_index);
  void signalUpdateConnectLidar(const std::size_t _lidar_index, const bool _en);
  void signalUpdateStartTest(const bool _en);
  int signalUpdateBlockMsgBox(const QString& _msg);
  void signalUpdateConnectPowerMeter(const bool _is_connect);
  void signalUpdateConnectRotator(const bool _is_connect);
  void signalRestart();

private:
  explicit AppEvent(QObject* _parent = nullptr) : QObject(_parent) {};

  std::unique_ptr<robosense::lidar::rsfsc_lib::WidgetLogSetting> mes_ { nullptr };
  std::unique_ptr<robosense::lidar::CsvParser> csv_info_ { nullptr };
  std::unique_ptr<MainWindow> main_window_ { nullptr };
  int current_module_idx_ { 0 };
  int current_one_to_x_num_ { 1 };
};

static robosense::lidar::AppEvent* const G_APP { robosense::lidar::AppEvent::getInstance() };
}  // namespace lidar
}  // namespace robosense

#endif  // APP_EVENT_H