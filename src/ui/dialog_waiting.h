﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef DIALOG_WAITING_H
#define DIALOG_WAITING_H

#include <QProgressDialog>
#include <functional>

namespace robosense::lidar
{
/**
 * @brief 弹出等待提示窗体，用于耗时操作UI等待
 * 
 */
class DialogWaiting : public QProgressDialog
{
  Q_OBJECT
public:
  explicit DialogWaiting(const QString& _msg, QWidget* _parent = nullptr);
  DialogWaiting(DialogWaiting&&)      = delete;
  DialogWaiting(const DialogWaiting&) = delete;
  DialogWaiting& operator=(DialogWaiting&&) = delete;
  DialogWaiting& operator=(const DialogWaiting&) = delete;
  ~DialogWaiting() override                      = default;
  void blockWaiting(const std::function<bool()>& _cb_is_waiting);
};
}  // namespace robosense::lidar

#endif  // DIALOG_WAITING_H