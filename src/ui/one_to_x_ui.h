﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ONE_TO_X_UI_H
#define ONE_TO_X_UI_H

#include <QtCore/QObject>

class QPushButton;
class QMainWindow;
class QGroupBox;
class QWidget;
class QVBoxLayout;

namespace robosense::lidar
{

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

/**
 * @brief 控制操作一托多UI类
 * 
 */
class OneToXUi : public QObject
{
  Q_OBJECT
public:
  explicit OneToXUi(const int _current_index, QMainWindow* _main_win, QObject* _parent = nullptr);
  OneToXUi(OneToXUi&&)      = delete;
  OneToXUi(const OneToXUi&) = delete;
  OneToXUi& operator=(OneToXUi&&) = delete;
  OneToXUi& operator=(const OneToXUi&) = delete;
  ~OneToXUi() override                 = default;

  [[nodiscard]] QWidget* getWidgetControl() const;
  [[nodiscard]] rsfsc_lib::WidgetLidarInfo* getWidgetLidarInfo() const;

private:
  void setupLayout();
  void createLidarInfo();

  QMainWindow* main_win_ { nullptr };

  // control
  rsfsc_lib::WidgetLidarInfo* widget_lidar_info_ { nullptr };
  QPushButton* pushbutton_connect_lidar_ { nullptr };
  QPushButton* pushbutton_test_ { nullptr };
  QGroupBox* groupbox_lidar_ctrl_ { nullptr };
  QVBoxLayout* layout_lidar_ctrl_ { nullptr };

  int current_lidar_index_ { 0 };
};
}  // namespace robosense::lidar

#endif  // ONE_TO_X_UI_H