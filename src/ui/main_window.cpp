﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"

#include "async_plugin_loader.h"
#include "data_serializer.h"
#include "flow_editor/side_panel/side_panel_node_widget.h"
#include "flow_editor/test_flow_editor.h"
#include "node_models/bt_node_model.h"
#include "node_models/node_manager.h"
#include "node_utils.h"
#include "plugin_manager.h"
#include "property/property_model.h"
#include "property/property_view.h"
#include "ui/app_event.h"
#include "ui/dialog_waiting.h"
#include "ui/message_browser.h"
#include "ui/one_to_x_ui.h"
#include <QApplication>
#include <QDir>
#include <QFileDialog>
#include <QLabel>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QPluginLoader>
#include <QScrollArea>
#include <QSplitter>
#include <QTimer>
#include <QtWidgets/QAction>
#include <QtWidgets/QDockWidget>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QTreeView>
#include <QtWidgets/QVBoxLayout>
#include <memory>
#include <qwidget.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent), splitter_display_(new QSplitter(Qt::Orientation::Vertical, this)), current_file_path_("")
{
  RSFSCLog::getInstance()->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  RSFSCLog::getInstance()->setTerminalLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);

  initPtr();

  // 创建菜单和工具栏
  createActions();

  browser_message_ = new robosense::lidar::rsfsc_lib::MessageBrowser(QString::fromUtf8(PROJECT_NAME), this);
  robosense::lidar::RSFSCLog::setQtLogWidget(browser_message_, "slotShowMessage");

  splitter_display_->addWidget(&ptr_->get<TestFlowEditor>());
  splitter_display_->addWidget(browser_message_);

  // 设置占比 widget1 : widget2 = 2 : 1
  splitter_display_->setStretchFactor(0, 5);
  splitter_display_->setStretchFactor(1, 3);

  QWidget* widget_main     = new QWidget;
  QHBoxLayout* layout_main = new QHBoxLayout(widget_main);
  layout_main->addWidget(splitter_display_);
  this->setCentralWidget(widget_main);

  // 设置默认的自动保存配置路径
  QDir config_dir(QApplication::applicationDirPath() + "/config");
  if (!config_dir.exists())
  {
    config_dir.mkpath(".");
  }
  auto_save_config_path_ = config_dir.absolutePath() + "/auto_save_config.json";
  LOG_INFO("设置自动保存配置路径: {}", auto_save_config_path_);

  allConnect();

  // 初始化参数设置
  initParamSettings();

  // 设置一托多UI
  setupOneToXUi();

  createDockWindows();
  G_APP->getMES()->addCheckSumDir(AppEvent::getConfigDir());
  QTimer::singleShot(50, this, []() { Utils::showWidget(G_APP->getMES()); });
}

MainWindow::~MainWindow()
{
  cleanBeforeQuit();

  // 释放插件加载对话框
  if (plugin_loading_dialog_ != nullptr)
  {
    delete plugin_loading_dialog_;
    plugin_loading_dialog_ = nullptr;
  }
}

void MainWindow::closeEvent(QCloseEvent* _event)
{
  LOG_INFO("FUC: closeEvent call back");
  QMessageBox::StandardButton ret = QMessageBox::warning(this, "警告", "<font color='red'>确定退出?</font>",
                                                         QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
  if (ret == QMessageBox::No)
  {
    _event->ignore();
    return;
  }

  // if (browser_message_->isVisible())
  // {
  //   browser_message_->close();
  // }

  Utils::closeWidget(&ptr_->get<PropertyView>(), false);
  Utils::closeWidget(G_APP->getMES(), false);
  // closeWidget(widget_about_, false);

  QMainWindow::closeEvent(_event);
}

OneToXUi* MainWindow::oneToXUi(int _index) const
{
  auto iter = all_one_to_x_ui_.find(_index);

  if (iter == all_one_to_x_ui_.end())
  {
    return nullptr;
  }

  return iter->second.get();
}

PropertyView* MainWindow::propertyView() const { return &ptr_->get<PropertyView>(); }

void MainWindow::cleanBeforeQuit()
{
  RSFSCLog::getInstance()->info("FUC: clean before quit");
  // writeSettings();
  // G_APP->destroyParam();
  G_APP->destroyMES();

  robosense::lidar::RSFSCLog::setQtLogWidget(nullptr);

  delete browser_message_;

  // delete widget_statusbar_;
  RSFSCLog::getInstance()->info("cleanBeforeQuit() finish");
}

void MainWindow::initParamSettings()
{
  // 注册雷达数量参数
  auto& property_view = ptr_->get<PropertyView>();
  property_view.setButtonHidden(PropertyView::BtnHidden { false, false, true, true, true });
  DataSerializer::getInstance()->setProperty(&property_view);
  auto model = property_view.model();
  if (!model)
  {
    model = std::make_shared<PropertyModel>();
    property_view.setModel(model);
  }

  model->registerProperty<IntItem>("雷达基本配置", "雷达数量", 1, 1, 16, 1);
  model->setPropertyDescription("雷达基本配置", "雷达数量", "雷达数量");
  // 设置该组为只读（不允许添加/删除属性）
  property_view.addReadOnlyGroup("雷达基本配置");

  auto is_file_exist = QFile::exists(auto_save_config_path_);
  QByteArray json_data;

  if (is_file_exist)
  {
    QFile file(auto_save_config_path_);
    if (!file.open(QIODevice::ReadOnly))
    {
      LOG_ERROR("无法打开配置文件: {}", auto_save_config_path_);
      return;
    }

    json_data = file.readAll();
    file.close();

    // 解析JSON以获取雷达数量
    try
    {
      nlohmann::json json = nlohmann::json::parse(json_data.toStdString());
      if (json.contains("groups") && json["groups"].contains("雷达基本配置") &&
          json["groups"]["雷达基本配置"].contains("雷达数量") &&
          json["groups"]["雷达基本配置"]["雷达数量"].contains("value"))
      {
        lidar_count_ = json["groups"]["雷达基本配置"]["雷达数量"]["value"];
      }
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("解析配置文件失败: {}", e.what());
      return;
    }
  }

  LOG_INFO("检测到雷达数量: {}", lidar_count_);
  // 创建雷达参数配置
  createLidarParameters(lidar_count_);

  if (is_file_exist)
  {
    // 加载配置
    if (!loadConfigWithLidarCount(json_data.toStdString()))
    {
      return;
    }
  }

  // 连接雷达数量变化信号
  QObject::connect(model.get(), &PropertyModel::signalValueChanged, this,
                   [this](const std::string& _group, const std::string& _key, const QVariant& _value) {
                     LOG_INFO("属性值变化: 组={}, 键={}, 值={}", _group, _key, _value.toString());
                     // 每次属性值变化时自动保存配置
                     autoSaveConfig();

                     if (_group == "雷达基本配置" && _key == "雷达数量")
                     {
                       // 当雷达数量变化时，重启软件
                       slotRestart();
                     }
                   });
}

// 根据雷达数量创建雷达参数配置
void MainWindow::createLidarParameters(int _lidar_count)
{
  auto& property_view = ptr_->get<PropertyView>();
  auto model          = property_view.model();

  // 暂时禁用UI刷新
  model->blockSignals(true);

  // 为每个雷达创建参数组
  for (int i = 1; i <= _lidar_count; ++i)
  {
    // 创建雷达参数组名称
    std::string group_name = "雷达" + std::to_string(i) + "固定参数";

    // 注册雷达基本参数
    model->registerProperty<BoolItem>(group_name, "项目编号是否可见", true);
    model->setPropertyDescription(group_name, "项目编号是否可见", "项目编号控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "雷达安装位置是否可见", true);
    model->setPropertyDescription(group_name, "雷达安装位置是否可见", "雷达安装位置控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "雷达SN是否可见", true);
    model->setPropertyDescription(group_name, "雷达SN是否可见", "雷达SN控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "雷达IP地址是否可见", false);
    model->setPropertyDescription(group_name, "雷达IP地址是否可见", "雷达IP地址控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "MSOP端口号是否可见", false);
    model->setPropertyDescription(group_name, "MSOP端口号是否可见", "MSOP端口号控件是否布局显示");

    model->registerProperty<BoolItem>(group_name, "DIFOP端口号是否可见", false);
    model->setPropertyDescription(group_name, "DIFOP端口号是否可见", "DIFOP端口号控件是否布局显示");

    // 设置该组为只读（不允许添加/删除属性）
    property_view.addReadOnlyGroup(QString::fromStdString(group_name));

    LOG_INFO("已创建雷达参数组: {}", group_name);
  }

  // 恢复信号
  model->blockSignals(false);

  // 手动触发一次模型变化信号，更新UI
  Q_EMIT model->signalModelChanged();
}

void MainWindow::setupOneToXUi()
{
  auto& property_view = ptr_->get<PropertyView>();
  auto model          = property_view.model();
  if (!model)
  {
    LOG_ERROR("setupOneToXUi : property_view model is null");
    return;
  }

  int lidar_count = model->getValue<int>("雷达基本配置", "雷达数量");

  for (int idx = 0; idx < lidar_count; ++idx)
  {
    all_one_to_x_ui_[idx + 1] = std::make_unique<OneToXUi>(idx + 1, this);
  }
}

void MainWindow::initPtr()
{
  auto property_view     = std::make_unique<PropertyView>();
  auto flow_editor       = std::make_unique<TestFlowEditor>();
  auto side_panel_widget = std::make_unique<SidePanelWidget>();
  ptr_ = std::make_unique<MainWindowPtr>(property_view.release(), flow_editor.release(), side_panel_widget.release());
}

void MainWindow::allConnect()
{
  QObject::connect(PluginManager::getInstance(), &PluginManager::signalAddPluginNodes, &ptr_->get<SidePanelWidget>(),
                   &SidePanelWidget::slotAddPluginNodes);
}

void MainWindow::createActions()
{
  // 文件菜单
  QMenu* file_menu = menuBar()->addMenu("文件");

  new_action_ = new QAction("新建", this);
  new_action_->setShortcut(QKeySequence::New);
  QObject::connect(new_action_, &QAction::triggered, this, &MainWindow::slotNewBt);
  file_menu->addAction(new_action_);

  open_action_ = new QAction("打开", this);
  open_action_->setShortcut(QKeySequence::Open);
  QObject::connect(open_action_, &QAction::triggered, this, &MainWindow::slotOpenFile);
  file_menu->addAction(open_action_);

  file_menu->addSeparator();

  save_action_ = new QAction("保存", this);
  save_action_->setShortcut(QKeySequence::Save);
  QObject::connect(save_action_, &QAction::triggered, this, &MainWindow::slotSaveFile);
  file_menu->addAction(save_action_);

  save_as_action_ = new QAction("另存为", this);
  save_as_action_->setShortcut(QKeySequence::SaveAs);
  QObject::connect(save_as_action_, &QAction::triggered, this, &MainWindow::slotSaveFileAs);
  file_menu->addAction(save_as_action_);

  file_menu->addSeparator();

  exit_action_ = new QAction("退出", this);
  exit_action_->setShortcut(QKeySequence::Quit);
  connect(exit_action_, &QAction::triggered, this, &QWidget::close);
  file_menu->addAction(exit_action_);

  // 工具菜单
  QMenu* tools_menu = menuBar()->addMenu("工具");

  load_plugins_action_ = new QAction("加载插件", this);
  QObject::connect(load_plugins_action_, &QAction::triggered, this, &MainWindow::slotLoadPlugins);
  tools_menu->addAction(load_plugins_action_);

  param_setting_action_ = new QAction("参数配置", this);
  QObject::connect(param_setting_action_, &QAction::triggered, this, &MainWindow::slotParamSetting);
  tools_menu->addAction(param_setting_action_);

  tools_menu->addSeparator();

  toggle_edit_lock_action_ = new QAction("🔓 解锁编辑", this);
  toggle_edit_lock_action_->setCheckable(true);
  toggle_edit_lock_action_->setShortcut(QKeySequence("Ctrl+L"));
  toggle_edit_lock_action_->setToolTip("锁定/解锁节点编辑功能，防止意外修改流程");
  QObject::connect(toggle_edit_lock_action_, &QAction::triggered, this, &MainWindow::slotToggleEditLock);
  tools_menu->addAction(toggle_edit_lock_action_);

  // 帮助菜单
  QMenu* help_menu = menuBar()->addMenu("帮助");

  about_action_ = new QAction("关于", this);
  QObject::connect(about_action_, &QAction::triggered, this, &MainWindow::slotAbout);
  help_menu->addAction(about_action_);

  // 工具栏
  QToolBar* file_tool_bar = addToolBar("文件");

  reorder_action_ = new QAction("节点对齐", this);
  QObject::connect(reorder_action_, &QAction::triggered, this, &MainWindow::slotReorderNode);

  file_tool_bar->addAction(new_action_);
  file_tool_bar->addAction(open_action_);
  file_tool_bar->addAction(save_action_);
  file_tool_bar->addSeparator();
  file_tool_bar->addAction(reorder_action_);
  file_tool_bar->addAction(toggle_edit_lock_action_);
}

void MainWindow::createDockWindows()
{
  // 节点列表停靠窗口
  nodes_dock_ = new QDockWidget("节点面板", this);
  nodes_dock_->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
  nodes_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);
  auto* widget_dock = new QWidget(nodes_dock_);

  QVBoxLayout* layout_control = new QVBoxLayout(widget_dock);
  layout_control->setSpacing(0);
  ptr_->get<SidePanelWidget>().setMimeType(QString(G_MIME_TYPE));
  ptr_->get<SidePanelWidget>().initializeBasicNodes();
  ptr_->get<SidePanelWidget>().setParent(widget_dock);
  layout_control->addWidget(&ptr_->get<SidePanelWidget>());
  widget_dock->setLayout(layout_control);
  nodes_dock_->setWidget(widget_dock);

  // 创建控制面板停靠窗口
  ctrl_dock_ = new QDockWidget("测试面板", this);
  ctrl_dock_->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
  ctrl_dock_->setFeatures(QDockWidget::DockWidgetMovable | QDockWidget::DockWidgetFloatable);
  pushbutton_start_test_ = new QPushButton(QString::fromUtf8("开始测试"), widget_dock);

  QWidget* widget_ctrl     = new QWidget(ctrl_dock_);
  QVBoxLayout* layout_ctrl = new QVBoxLayout(widget_ctrl);
  layout_ctrl->setSpacing(5);
  layout_ctrl->setContentsMargins(5, 5, 5, 5);

  for (const auto& [index, one_ui] : all_one_to_x_ui_)
  {
    if (one_ui && one_ui->getWidgetControl() != nullptr)
    {
      layout_ctrl->addWidget(one_ui->getWidgetControl());
    }
  }
  layout_ctrl->addStretch();
  layout_ctrl->addWidget(pushbutton_start_test_);
  widget_ctrl->setLayout(layout_ctrl);
  ctrl_dock_->setWidget(widget_ctrl);

  // 将两个停靠窗口添加到底部区域
  addDockWidget(Qt::LeftDockWidgetArea, nodes_dock_);
  addDockWidget(Qt::LeftDockWidgetArea, ctrl_dock_);

  // 将两个停靠窗口标签化，这样它们会共享同一个区域，并通过标签切换
  tabifyDockWidget(nodes_dock_, ctrl_dock_);

  // 默认显示第一个标签页
  nodes_dock_->raise();

  QObject::connect(pushbutton_start_test_, &QPushButton::clicked, this, &MainWindow::slotStartTest);
}

void MainWindow::slotNewBt()
{
  // editor_->newTree();
  current_file_path_ = "";
}

void MainWindow::slotOpenFile()
{
  QString file_path =
    QFileDialog::getOpenFileName(this, "打开测试工程文件", "", "测试工程文件 (*.rsfsc);;所有文件 (*)");
  if (file_path.isEmpty())
  {
    return;
  }

  if (!DataSerializer::getInstance()->loadFromUnifiedFile(file_path))
  {
    QMessageBox::critical(this, "错误", "无法加载测试工程文件: " + file_path);
    return;
  }

  current_file_path_ = file_path;
  statusBar()->showMessage("已加载: " + file_path);
}

void MainWindow::slotSaveFile()
{
  QString file_path =
    QFileDialog::getSaveFileName(this, "保存测试工程文件", "", "测试工程文件 (*.rsfsc);;所有文件 (*)");
  if (file_path.isEmpty())
  {
    return;
  }

  // 检查文件路径是否已经包含.rsfsc后缀，如果没有则自动添加
  if (!file_path.endsWith(".rsfsc", Qt::CaseInsensitive))
  {
    file_path += ".rsfsc";
  }

  current_file_path_ = file_path;
  if (!DataSerializer::getInstance()->saveToUnifiedFile(current_file_path_))
  {
    QMessageBox::critical(this, "错误", "无法保存rsfsc文件: " + current_file_path_);
    return;
  }

  // 同时更新自动保存的配置文件路径
  auto_save_config_path_ = current_file_path_;

  LOG_INFO("保存: {} 成功", current_file_path_);
  statusBar()->showMessage("已保存: " + current_file_path_);
}

void MainWindow::slotSaveFileAs()
{
  QString file_path =
    QFileDialog::getSaveFileName(this, "另存为测试工程文件", "", "测试工程文件 (*.rsfsc);;所有文件 (*)");
  if (file_path.isEmpty())
  {
    return;
  }

  // 检查文件路径是否已经包含.rsfsc后缀，如果没有则自动添加
  if (!file_path.endsWith(".rsfsc", Qt::CaseInsensitive))
  {
    file_path += ".rsfsc";
  }

  if (!DataSerializer::getInstance()->saveToUnifiedFile(file_path))
  {
    QMessageBox::critical(this, "错误", "无法保存rsfsc文件: " + file_path);
    return;
  }

  current_file_path_ = file_path;
  // 同时更新自动保存的配置文件路径
  auto_save_config_path_ = current_file_path_;

  LOG_INFO("另存为: {} 成功", current_file_path_);
  statusBar()->showMessage("已保存: " + current_file_path_);
}

void MainWindow::slotLoadPlugins()
{
  QString plugin_dir = QFileDialog::getExistingDirectory(this, "选择插件目录", QApplication::applicationDirPath());
  if (plugin_dir.isEmpty())
  {
    return;
  }

  // 创建插件加载等待对话框
  if (plugin_loading_dialog_ != nullptr)
  {
    delete plugin_loading_dialog_;
    plugin_loading_dialog_ = nullptr;
  }

  plugin_loading_dialog_ = new DialogWaiting("正在加载插件，请稍候...", this);

  // 启动异步加载
  AsyncPluginLoader::getInstance()->startLoading(plugin_dir);

  // 显示等待对话框并阻塞等待加载完成
  plugin_loading_dialog_->blockWaiting([]() { return AsyncPluginLoader::getInstance()->isLoading(); });

  // 加载完成后显示结果
  LOG_INFO("插件加载已完成");
}

void MainWindow::slotParamSetting() { Utils::showWidget(&ptr_->get<PropertyView>()); }

void MainWindow::slotAbout() {}

void MainWindow::slotStartTest()
{
  // 配置全局的BlockBoard
  NodeManager::getInstance()->globalBlackboard()->set("g_widget_log_setting", G_APP->getMES());

  lidar_info_widgets_.clear();
  for (const auto& [index, one_ui] : all_one_to_x_ui_)
  {
    if (one_ui && one_ui->getWidgetLidarInfo() != nullptr)
    {
      lidar_info_widgets_[index] = one_ui->getWidgetLidarInfo();
    }
  }
  NodeManager::getInstance()->globalBlackboard()->set("g_lidar_info_widgets", &lidar_info_widgets_);
  NodeManager::getInstance()->globalBlackboard()->set("current_lidar_index", 1);

  ptr_->get<TestFlowEditor>().startTest();
}

void MainWindow::slotReorderNode() { ptr_->get<TestFlowEditor>().nodeReorder(); }

void MainWindow::slotRestart()
{
  QMessageBox::information(&ptr_->get<PropertyView>(), QString::fromUtf8("提示"), QString::fromUtf8("即将重启软件."),
                           QMessageBox::Ok);
  close();
  QApplication::exit(MainWindow::restartCode());
}

void MainWindow::slotToggleEditLock()
{
  is_edit_locked_ = !is_edit_locked_;

  // 更新菜单项文本和图标
  if (is_edit_locked_)
  {
    toggle_edit_lock_action_->setText("🔒 锁定编辑");
    toggle_edit_lock_action_->setToolTip("当前已锁定编辑，点击解锁");
    toggle_edit_lock_action_->setChecked(true);
    statusBar()->showMessage("节点编辑已锁定，防止意外修改", 3000);
  }
  else
  {
    toggle_edit_lock_action_->setText("🔓 解锁编辑");
    toggle_edit_lock_action_->setToolTip("当前可以编辑，点击锁定");
    toggle_edit_lock_action_->setChecked(false);
    statusBar()->showMessage("节点编辑已解锁，可以进行修改", 3000);
  }

  // 通知FlowScene更新编辑状态
  auto& flow_editor = ptr_->get<TestFlowEditor>();
  flow_editor.setEditLocked(is_edit_locked_);

  LOG_INFO("节点编辑锁定状态: {}", is_edit_locked_ ? "已锁定" : "已解锁");
}

void MainWindow::autoSaveConfig()
{
  // 如果正在加载配置，不进行自动保存
  if (is_loading_config_)
  {
    return;
  }

  // 如果自动保存路径为空，使用默认路径
  if (auto_save_config_path_.isEmpty())
  {
    // 获取应用程序目录下的config子目录
    QDir config_dir(QApplication::applicationDirPath() + "/config");
    if (!config_dir.exists())
    {
      config_dir.mkpath(".");
    }
    auto_save_config_path_ = config_dir.absolutePath() + "/auto_save_config.json";
    LOG_INFO("设置自动保存配置路径: {}", auto_save_config_path_);
  }

  if (!ptr_->get<PropertyView>().saveToJson(auto_save_config_path_))
  {
    LOG_ERROR("自动保存配置失败: {}", auto_save_config_path_);
    return;
  }

  LOG_INFO("自动保存配置到: {}", auto_save_config_path_);
}

bool MainWindow::loadConfig(const QString& _file_path)
{
  if (_file_path.isEmpty())
  {
    LOG_WARN("加载配置文件路径为空，将使用默认配置");
    return true;  // 返回true，使用默认配置继续执行
  }

  // 检查文件是否存在
  QFileInfo file_info(_file_path);
  if (!file_info.exists() || !file_info.isFile())
  {
    LOG_WARN("配置文件不存在: {}，将使用默认配置", _file_path);
    return true;  // 返回true，使用默认配置继续执行
  }

  // 设置加载标志，防止在加载过程中触发自动保存
  is_loading_config_ = true;

  bool result = ptr_->get<PropertyView>().loadFromJson(_file_path);

  if (result)
  {
    LOG_INFO("成功加载配置: {}", _file_path);
    current_file_path_ = _file_path;
    statusBar()->showMessage("已加载: " + _file_path);
  }
  else
  {
    LOG_ERROR("加载配置失败: {}", _file_path);
  }

  // 重置加载标志
  is_loading_config_ = false;

  return result;
}

bool MainWindow::loadConfigWithLidarCount(const std::string& _json)
{
  auto& property_view = ptr_->get<PropertyView>();

  try
  {
    // 创建要排除的组列表
    std::vector<std::string> exclude_groups;
    for (int i = lidar_count_ + 1; i <= 16; ++i)
    {
      exclude_groups.push_back("雷达" + std::to_string(i) + "固定参数");
    }

    property_view.loadFromJsonExcludeGroups(_json, exclude_groups);
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("解析配置文件失败: {}", e.what());
    return false;
  }

  return true;
}
}  // namespace robosense::lidar
