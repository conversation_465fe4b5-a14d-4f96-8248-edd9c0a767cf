﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "dialog_waiting.h"
#include <QEventLoop>
#include <QIcon>
#include <QTimer>

namespace robosense::lidar
{

DialogWaiting::DialogWaiting(const QString& _msg, QWidget* _parent) : QProgressDialog(_parent)
{
  this->setWindowFlags(Qt::Dialog | Qt::WindowMinimizeButtonHint);
  this->setWindowIcon(QIcon(":/img/logo.bmp"));
  this->setMaximum(0);
  this->setMinimum(0);
  this->setValue(0);
  this->setWindowTitle(QString::fromUtf8("请等待..."));
  this->setLabelText(_msg);
  this->setEnabled(false);
}

void DialogWaiting::blockWaiting(const std::function<bool()>& _cb_is_waiting)
{
  this->show();
  int wait_counter           = 0;
  constexpr int LOOP_COUNT   = 90;
  constexpr int WAITING_TIME = 500;
  while (++wait_counter < LOOP_COUNT)
  {
    QEventLoop event_loop;
    QTimer::singleShot(WAITING_TIME, &event_loop, &QEventLoop::quit);
    event_loop.exec();
    if (!_cb_is_waiting())
    {
      break;
    }
  }
  this->close();
}

}  // namespace robosense::lidar
