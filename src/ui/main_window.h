﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H
#include "flow_editor/side_panel/side_panel_node_widget.h"
#include "ptr_container.h"
#include <QString>
#include <QtCore/QObject>
#include <QtWidgets/QMainWindow>
#include <memory>
#include <unordered_map>

class QPushButton;
class QSplitter;
class LogTestStatus;
class QCloseEvent;
class QDockWidget;
class QAction;

namespace robosense::lidar
{
class WidgetLidarInfo;
class WidgetStatusbar;
class WidgetLogSetting;
class WidgetAbout;
class PropertyView;
class TestFlowEditor;
class SidePanelWidget;
class OneToXUi;
class DialogWaiting;
namespace rsfsc_lib
{
class MessageBrowser;
class WidgetLidarInfo;
}  // namespace rsfsc_lib

using MainWindowPtr = comm::PtrContainer<PropertyView, TestFlowEditor, SidePanelWidget>;

class MainWindow : public QMainWindow
{
  Q_OBJECT

public:
  explicit MainWindow(QWidget* _parent = nullptr);
  ~MainWindow() override;

  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  MainWindow(MainWindow&&)                 = delete;
  MainWindow& operator=(MainWindow&&) = delete;

  static constexpr int restartCode() { return -112233550; }
  void closeEvent(QCloseEvent* _event) override;
  [[nodiscard]] OneToXUi* oneToXUi(int _index) const;
  [[nodiscard]] PropertyView* propertyView() const;

Q_SIGNALS:
  void signalAddPluginNodes(const robosense::lidar::NodeInfos& _node_infos);

private Q_SLOTS:
  void slotNewBt();
  void slotOpenFile();
  void slotSaveFile();
  void slotSaveFileAs();
  void slotLoadPlugins();
  void slotParamSetting();
  void slotAbout();
  void slotStartTest();
  void slotReorderNode();
  void slotRestart();
  void slotToggleEditLock();

private:
  void createActions();
  void createDockWindows();
  void initPtr();
  void allConnect();
  void cleanBeforeQuit();
  void initParamSettings();
  void createLidarParameters(int _lidar_count);
  void setupOneToXUi();

  // 自动保存配置
  void autoSaveConfig();

  // 加载配置
  bool loadConfig(const QString& _file_path);
  bool loadConfigWithLidarCount(const std::string& _json);

  // 菜单动作
  QAction* new_action_ { nullptr };
  QAction* open_action_ { nullptr };
  QAction* save_action_ { nullptr };
  QAction* save_as_action_ { nullptr };
  QAction* exit_action_ { nullptr };
  QAction* load_plugins_action_ { nullptr };
  QAction* param_setting_action_ { nullptr };
  QAction* about_action_ { nullptr };
  QAction* reorder_action_ { nullptr };
  QAction* toggle_edit_lock_action_ { nullptr };

  QPushButton* pushbutton_start_test_ { nullptr };

  // 停靠窗口
  QDockWidget* nodes_dock_ { nullptr };
  QDockWidget* ctrl_dock_ { nullptr };
  QDockWidget* properties_dock_ { nullptr };
  QDockWidget* monitor_dock_ { nullptr };

  QSplitter* splitter_display_ { nullptr };
  rsfsc_lib::MessageBrowser* browser_message_ { nullptr };
  std::unique_ptr<MainWindowPtr> ptr_ { nullptr };
  std::unordered_map<int, std::unique_ptr<OneToXUi>> all_one_to_x_ui_;
  std::unordered_map<int, rsfsc_lib::WidgetLidarInfo*> lidar_info_widgets_;

  // 当前文件路径
  QString current_file_path_;

  // 自动保存配置的文件路径
  QString auto_save_config_path_;

  // 是否正在加载配置（防止加载时触发自动保存）
  bool is_loading_config_ { false };

  // 编辑锁定状态
  bool is_edit_locked_ { false };

  int lidar_count_ { 1 };

  // 插件加载等待对话框
  DialogWaiting* plugin_loading_dialog_ { nullptr };
};

}  // namespace robosense::lidar

#endif  // MAIN_WINDOW_H
