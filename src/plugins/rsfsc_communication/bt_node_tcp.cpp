﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "bt_node_tcp.h"
#include "behaviortree_cpp/basic_types.h"
#include "mems_tcp.h"
#include <future>
#include <memory>
#include <rsfsc_log/fmt/bundled/ranges.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

// 辅助函数实现

bool strToUint32(const std::string& _str, uint32_t& _out_value)
{
  try
  {
    LOG_DEBUG("strToUint32 input: [{}]", _str);

    // 使用 std::stoul 的 base=0 参数，它会自动检测进制
    // 如果字符串以 0x 开头，它会被解析为十六进制
    _out_value = std::stoul(_str, nullptr, 0);

    LOG_DEBUG("strToUint32 converted: [{}] to {:#x}", _str, _out_value);
    return true;
  }
  catch (const std::invalid_argument& e)
  {
    LOG_ERROR("Invalid input string [{}]: {}", _str, std::string(e.what()));
  }
  catch (const std::out_of_range& e)
  {
    LOG_ERROR("Out of range for uint32_t [{}]: {}", _str, std::string(e.what()));
  }
  return false;
}

namespace
{
// 辅助函数：去除字符串前后的空白字符
std::string trimString(const std::string& _str)
{
  if (_str.empty())
  {
    return _str;
  }

  size_t first = _str.find_first_not_of(" \t\r\n");
  if (first == std::string::npos)
  {
    return "";
  }

  size_t last = _str.find_last_not_of(" \t\r\n");
  return _str.substr(first, last - first + 1);
}

// 辅助函数：检查地址是否4字节对齐
bool isAddressAligned(uint32_t _addr) { return (_addr % 4) == 0; }

// 辅助函数：处理单个地址
bool processSingleAddress(const std::string& _addr_str, std::vector<uint32_t>& _out_addresses)
{
  uint32_t addr = 0;
  if (!strToUint32(_addr_str, addr))
  {
    LOG_ERROR("parseAddressString: Invalid address format [{}]", _addr_str);
    return false;
  }

  if (!isAddressAligned(addr))
  {
    LOG_ERROR("parseAddressString: Address not 4-byte aligned [{}]", _addr_str);
    return false;
  }

  _out_addresses.push_back(addr);
  return true;
}

// 辅助函数：处理地址范围
// 参数说明：
// _start_str: 范围起始地址字符串
// _end_str: 范围结束地址字符串
// _original_str: 原始输入字符串（用于错误日志）
// _out_addresses: 输出地址列表
bool processAddressRange(const std::string& _start_str,
                         const std::string& _end_str,
                         const std::string& _original_str,
                         std::vector<uint32_t>& _out_addresses)
{
  uint32_t start_addr = 0;
  uint32_t end_addr   = 0;

  if (!strToUint32(_start_str, start_addr) || !strToUint32(_end_str, end_addr))
  {
    LOG_ERROR("parseAddressString: Invalid range format [{}]", _original_str);
    return false;
  }

  if (start_addr > end_addr)
  {
    LOG_ERROR("parseAddressString: Start address greater than end address [{}]", _original_str);
    return false;
  }

  if (!isAddressAligned(start_addr))
  {
    LOG_ERROR("parseAddressString: Start address not 4-byte aligned [{}]", _original_str);
    return false;
  }

  // 添加范围内的所有地址（按4字节对齐）
  for (uint32_t addr = start_addr; addr <= end_addr; addr += 4)
  {
    _out_addresses.push_back(addr);
  }

  return true;
}
}  // namespace

bool parseAddressString(const std::string& _addr_str, std::vector<uint32_t>& _out_addresses)
{
  _out_addresses.clear();

  // 卫语句：检查输入是否为空
  if (_addr_str.empty())
  {
    LOG_ERROR("parseAddressString: Empty address string");
    return false;
  }

  // 分割字符串，处理逗号分隔的部分
  std::istringstream stream(_addr_str);
  std::string current_part;

  while (std::getline(stream, current_part, ','))
  {
    // 去除前后空格
    current_part = trimString(current_part);

    // 跳过空部分
    if (current_part.empty())
    {
      continue;
    }

    // 检查是否是范围表示 (包含 '-')
    size_t dash_pos = current_part.find('-');

    if (dash_pos != std::string::npos)
    {
      // 范围格式: "0x83c00100-0x83c00120"
      std::string start_str = trimString(current_part.substr(0, dash_pos));
      std::string end_str   = trimString(current_part.substr(dash_pos + 1));

      if (!processAddressRange(start_str, end_str, current_part, _out_addresses))
      {
        return false;
      }
    }
    else
    {
      // 单个地址格式: "0x83c00100"
      if (!processSingleAddress(current_part, _out_addresses))
      {
        return false;
      }
    }
  }

  // 卫语句：检查是否解析出了有效地址
  if (_out_addresses.empty())
  {
    LOG_ERROR("parseAddressString: No valid addresses found in [{}]", _addr_str);
    return false;
  }

  LOG_DEBUG("parseAddressString: Successfully parsed {} addresses from [{}]", _out_addresses.size(), _addr_str);
  return true;
}

// SequenceWithFailureCheck 实现
SequenceWithFailureCheck::SequenceWithFailureCheck(const std::string& _name, const BT::NodeConfig& _config) :
  BT::ControlNode(_name, _config)
{}

BT::PortsList SequenceWithFailureCheck::providedPorts() { return {}; }

BT::NodeStatus SequenceWithFailureCheck::tick()
{
  bool any_failed = false;

  for (size_t index = 0; index < childrenCount(); ++index)
  {
    TreeNode* child_node = children_nodes_[index];

    BT::NodeStatus child_status = child_node->executeTick();

    if (child_status == BT::NodeStatus::RUNNING)
    {
      // 当前有子节点在运行，返回 RUNNING（保持行为树一致性）
      return BT::NodeStatus::RUNNING;
    }
    if (child_status == BT::NodeStatus::FAILURE)
    {
      any_failed = true;
    }
  }

  return any_failed ? BT::NodeStatus::FAILURE : BT::NodeStatus::SUCCESS;
}

// TcpConnect 实现
TcpConnect::TcpConnect(const std::string& _name, const BT::NodeConfig& _config) : BT::CoroActionNode(_name, _config) {}

BT::PortsList TcpConnect::providedPorts()
{
  return { BT::InputPort<std::string>("ip_address", "Lidar IP address"),
           BT::InputPort<uint16_t>("port", "Lidar MSOP port"),
           BT::InputPort<uint32_t>("timeout_ms", 5000, "Connection timeout in milliseconds"),
           BT::OutputPort<std::shared_ptr<MEMSTCP>>("tcp_handle", "Handle to the MEMSTCP instance") };
}

BT::NodeStatus TcpConnect::tick()
{
  mems_tcp_handle_ = std::make_shared<MEMSTCP>();

  auto ip_result      = getInput<std::string>("ip_address");
  auto port_result    = getInput<uint16_t>("port");
  auto timeout_result = getInput<uint32_t>("timeout_ms");

  if (!ip_result || !port_result || !timeout_result)
  {
    LOG_ERROR("{}: Missing required input ports (ip_address, port, timeout_ms)", name());
    return BT::NodeStatus::FAILURE;
  }

  std::string local_ip   = ip_result.value();
  uint16_t local_port    = port_result.value();
  uint32_t local_timeout = timeout_result.value();

  // 启动异步连接操作
  connection_future_ = std::async(std::launch::async, [handle = mems_tcp_handle_, ip_addr = std::move(local_ip),
                                                       port = local_port, timeout = local_timeout]() {
    // 调用实际的连接函数
    return handle->connect(ip_addr, port, timeout);
  });

  LOG_DEBUG("{}: Connection attempt started asynchronously for {}:{}", name(), local_ip, local_port);

  // 第一次 tick：启动操作并等待
  setStatusRunningAndYield();

  // --- 协程暂停点 --- (恢复执行)

  // 检查异步操作结果
  if (!connection_future_.valid())
  {
    LOG_ERROR("{}: Internal error - future is not valid after yield", name());
    return BT::NodeStatus::FAILURE;
  }

  // 轮询 future 状态
  auto future_status = connection_future_.wait_for(std::chrono::milliseconds(0));
  while (future_status == std::future_status::timeout)
  {
    // 操作仍在进行中，再次暂停协程
    setStatusRunningAndYield();
    // --- 协程暂停点 --- (再次恢复)
    future_status = connection_future_.wait_for(std::chrono::milliseconds(0));
  }

  // 获取结果并返回最终状态
  if (future_status == std::future_status::ready)
  {
    try
    {
      if (!connection_future_.get())
      {
        LOG_ERROR("{}: Future is not valid after getting result", name());
        return BT::NodeStatus::FAILURE;
      }

      LOG_DEBUG("{}: Connection attempt finished. Successful", name());
      // config().blackboard->set("dev_handle", device_handle);
      setOutput("tcp_handle", mems_tcp_handle_);
      return BT::NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("{}: Exception caught while getting connection future result: {}", name(), e.what());
      return BT::NodeStatus::FAILURE;
    }
  }
  else
  {
    LOG_ERROR("{}: Future status indicates an issue (not ready or timeout after loop)", name());
    return BT::NodeStatus::FAILURE;
  }
}

void TcpConnect::halt()
{
  LOG_DEBUG("{}: Halt requested during connection attempt", name());
  // 尝试中止底层的 MEMSTCP 操作
  if (mems_tcp_handle_)
  {
    // 假设 abort() 能影响 connect() 或设置一个标志让 connect() 提前退出
    mems_tcp_handle_->abort();
  }
  // 清理协程状态并重置 future
  CoroActionNode::halt();
  connection_future_ = {};   // 重置 future
  mems_tcp_handle_.reset();  // 释放句柄
}

// TcpDisconnect 实现
TcpDisconnect::TcpDisconnect(const std::string& _name, const BT::NodeConfig& _config) :
  BT::StatefulActionNode(_name, _config)
{}

BT::PortsList TcpDisconnect::providedPorts()
{
  return { BT::InputPort<std::shared_ptr<MEMSTCP>>("tcp_handle", "Handle to the MEMSTCP instance") };
}

BT::NodeStatus TcpDisconnect::onStart()
{
  auto handle_result = getInput<std::shared_ptr<MEMSTCP>>("tcp_handle");
  if (!handle_result || !handle_result.value())
  {
    LOG_ERROR("{}: Missing or invalid MEMSTCP handle", name());
    return BT::NodeStatus::FAILURE;
  }
  mems_tcp_handle_ = handle_result.value();

  // 异步执行 disconnect
  disconnect_future_ = std::async(std::launch::async, [handle = mems_tcp_handle_]() { return handle->disconnect(); });
  LOG_DEBUG("{}: Disconnection started asynchronously", name());
  return BT::NodeStatus::RUNNING;
}

BT::NodeStatus TcpDisconnect::onRunning()
{
  if (!disconnect_future_.valid())
  {
    LOG_ERROR("{}: Internal error - future is not valid during running state", name());
    return BT::NodeStatus::FAILURE;
  }

  auto future_status = disconnect_future_.wait_for(std::chrono::milliseconds(0));
  if (future_status == std::future_status::ready)
  {
    try
    {
      bool success_flag = disconnect_future_.get();
      LOG_DEBUG("{}: Disconnection finished. Success: {}", name(), success_flag);
      return success_flag ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("{}: Exception caught while getting disconnect future result: {}", name(), e.what());
      return BT::NodeStatus::FAILURE;
    }
  }
  else if (future_status == std::future_status::timeout)
  {
    return BT::NodeStatus::RUNNING;  // 继续运行
  }
  else
  {
    LOG_ERROR("{}: Future status indicates an issue during disconnect", name());
    return BT::NodeStatus::FAILURE;
  }
}

void TcpDisconnect::onHalted()
{
  LOG_DEBUG("{}: Halt requested during disconnection", name());
  // 通常无法中止 disconnect，只重置状态
  disconnect_future_ = {};   // 重置 future
  mems_tcp_handle_.reset();  // 释放句柄
  // 注意：基类的 halt() 会在 StatefulActionNode 的 tick 返回非 RUNNING 时自动调用
}

// TcpReadReg 实现
TcpReadReg::TcpReadReg(const std::string& _name, const BT::NodeConfig& _config) : BT::CoroActionNode(_name, _config) {}

BT::PortsList TcpReadReg::providedPorts()
{
  return { BT::InputPort<std::shared_ptr<MEMSTCP>>("tcp_handle", "Handle to the MEMSTCP instance"),
           BT::InputPort<std::string>("reg_addrs", "寄存器地址，支持多种格式：逗号分隔列表、范围表示或混合模式"),
           BT::InputPort<uint32_t>("timeout_ms", 100, "Timeout for the read operation (in 10ms units)"),
           BT::OutputPort<std::vector<int32_t>>("read_values", "Vector containing the read register values") };
}

BT::NodeStatus TcpReadReg::tick()
{
  auto handle_result = getInput<std::shared_ptr<MEMSTCP>>("tcp_handle");
  if (!handle_result || !handle_result.value())
  {
    LOG_ERROR("{}: Missing or invalid MEMSTCP handle", name());
    return BT::NodeStatus::FAILURE;
  }
  mems_tcp_handle_ = handle_result.value();

  auto timeout_result    = getInput<uint32_t>("timeout_ms");
  uint32_t local_timeout = timeout_result.value();

  auto addr_str_result = getInput<std::string>("reg_addrs");
  if (!addr_str_result)
  {
    LOG_ERROR("{}: Missing required input port 'reg_addrs'", name());
    return BT::NodeStatus::FAILURE;
  }

  // 定义返回类型别名
  using ReadResult = std::pair<bool, std::vector<int32_t>>;

  // 解析地址字符串
  std::string addr_str = addr_str_result.value();
  std::vector<uint32_t> local_addresses;

  // 解析地址字符串
  if (!parseAddressString(addr_str, local_addresses))
  {
    LOG_ERROR("{}: Failed to parse address string: [{}]", name(), addr_str);
    return BT::NodeStatus::FAILURE;
  }

  LOG_DEBUG("{}: local_addresses : {:x}", name(), fmt::join(local_addresses, ", "));

  // 启动异步读取
  read_future_ = std::async(
    std::launch::async,
    [handle = mems_tcp_handle_, addrs = std::move(local_addresses), timeout = local_timeout]() -> ReadResult {
      std::vector<int32_t> read_vals;
      bool success = handle->readRegData(addrs, read_vals, timeout);
      return { success, std::move(read_vals) };
    });
  LOG_DEBUG("{}: Read operation started asynchronously", name());

  setStatusRunningAndYield();  // 暂停

  // --- 恢复点 ---

  if (!read_future_.valid())
  {
    LOG_ERROR("{}: Internal error - future is not valid after yield", name());
    return BT::NodeStatus::FAILURE;
  }

  // 轮询
  auto future_status = read_future_.wait_for(std::chrono::milliseconds(0));
  while (future_status == std::future_status::timeout)
  {
    setStatusRunningAndYield();  // 继续等待
    future_status = read_future_.wait_for(std::chrono::milliseconds(0));
  }

  // 处理结果
  if (future_status == std::future_status::ready)
  {
    try
    {
      ReadResult operation_result      = read_future_.get();
      bool success_flag                = operation_result.first;
      std::vector<int32_t> result_data = std::move(operation_result.second);

      if (!success_flag)
      {
        LOG_ERROR("{}: Read operation failed (reported by MEMSTCP)", name());
        return BT::NodeStatus::FAILURE;
      }

      LOG_DEBUG("{}: Read operation successful, result_data : {:x}", name(), fmt::join(result_data, ", "));
      setOutput("read_values", result_data);
      return BT::NodeStatus::SUCCESS;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("{}: Exception caught while getting read future result: {}", name(), e.what());
      return BT::NodeStatus::FAILURE;
    }
  }
  else
  {
    LOG_ERROR("{}: Future status indicates an issue (not ready or timeout after loop)", name());
    return BT::NodeStatus::FAILURE;
  }
}

void TcpReadReg::halt()
{
  LOG_DEBUG("{}: Halt requested during read operation", name());
  if (mems_tcp_handle_)
  {
    mems_tcp_handle_->abort();
  }
  CoroActionNode::halt();
  read_future_ = {};  // 重置 future
  mems_tcp_handle_.reset();
}

// TcpWriteReg 实现
TcpWriteReg::TcpWriteReg(const std::string& _name, const BT::NodeConfig& _config) : BT::CoroActionNode(_name, _config)
{}

BT::PortsList TcpWriteReg::providedPorts()
{
  return { BT::InputPort<std::shared_ptr<MEMSTCP>>("tcp_handle", "Handle to the MEMSTCP instance"),
           BT::InputPort<std::string>("reg_addrs", "寄存器地址，支持多种格式：逗号分隔列表、范围表示或混合模式"),
           BT::InputPort<std::vector<int32_t>>(
             "reg_values",
             "寄存器值，支持多种格式：逗号分隔列表、范围表示或混合模式 支持多种格式：逗号分隔列表、范围表示或混合模式"),
           BT::InputPort<uint32_t>("timeout_ms", 100, "Timeout for the write operation (in 10ms units)") };
}

BT::NodeStatus TcpWriteReg::tick()
{
  auto handle_result = getInput<std::shared_ptr<MEMSTCP>>("tcp_handle");
  if (!handle_result || !handle_result.value())
  {
    LOG_ERROR("{}: Missing or invalid MEMSTCP handle", name());
    return BT::NodeStatus::FAILURE;
  }
  mems_tcp_handle_ = handle_result.value();

  auto addr_str_result = getInput<std::string>("reg_addrs");
  auto values_result   = getInput<std::vector<int32_t>>("reg_values");
  auto timeout_result  = getInput<uint32_t>("timeout_ms");
  auto path_result     = getInput<std::string>("download_data_path");  // 可选

  if (!addr_str_result || !values_result)
  {
    LOG_ERROR("{}: Missing required input ports (reg_addrs, reg_values)", name());
    return BT::NodeStatus::FAILURE;
  }

  // 解析地址字符串
  std::string addr_str = addr_str_result.value();
  std::vector<uint32_t> local_addresses;

  if (!parseAddressString(addr_str, local_addresses))
  {
    LOG_ERROR("{}: Failed to parse address string: [{}]", name(), addr_str);
    return BT::NodeStatus::FAILURE;
  }

  std::vector<int32_t> local_values = values_result.value();
  uint32_t local_timeout            = timeout_result.value_or(100);  // 使用默认值

  // 检查地址和值的数量是否匹配
  if (local_addresses.size() != local_values.size())
  {
    LOG_ERROR("{}: Mismatch between number of addresses ({}) and values ({})", name(), local_addresses.size(),
              local_values.size());

    // 如果值的数量少于地址数量，可以尝试重复最后一个值填充
    if (!local_values.empty() && local_values.size() < local_addresses.size())
    {
      LOG_DEBUG("{}: Attempting to fill missing values with the last value", name());
      int32_t last_value = local_values.back();
      local_values.resize(local_addresses.size(), last_value);
    }
    else
    {
      return BT::NodeStatus::FAILURE;
    }
  }

  if (local_addresses.empty())
  {
    LOG_ERROR("{}: 没有寄存器地址可操作", name());
    return BT::NodeStatus::FAILURE;
  }

  // 启动异步写入
  write_future_ = std::async(
    std::launch::async, [handle = mems_tcp_handle_, addrs = std::move(local_addresses), vals = std::move(local_values),
                         timeout = local_timeout]() { return handle->writeRegData(addrs, vals, timeout); });

  LOG_DEBUG("{}: Write operation started asynchronously", name());

  setStatusRunningAndYield();  // 暂停

  // --- 恢复点 ---

  if (!write_future_.valid())
  {
    LOG_ERROR("{}: Internal error - future is not valid after yield", name());
    return BT::NodeStatus::FAILURE;
  }

  // 轮询
  auto future_status = write_future_.wait_for(std::chrono::milliseconds(0));
  while (future_status == std::future_status::timeout)
  {
    setStatusRunningAndYield();  // 继续等待
    future_status = write_future_.wait_for(std::chrono::milliseconds(0));
  }

  // 处理结果
  if (future_status == std::future_status::ready)
  {
    try
    {
      bool success_flag = write_future_.get();
      LOG_DEBUG("{}: Write operation finished. Success: {}", name(), success_flag);
      return success_flag ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("{}: Exception caught while getting write future result: {}", name(), e.what());
      return BT::NodeStatus::FAILURE;
    }
  }
  else
  {
    LOG_ERROR("{}: Future status indicates an issue (not ready or timeout after loop)", name());
    return BT::NodeStatus::FAILURE;
  }
}

void TcpWriteReg::halt()
{
  LOG_DEBUG("{}: Halt requested during write operation", name());
  if (mems_tcp_handle_)
  {
    mems_tcp_handle_->abort();
  }
  CoroActionNode::halt();
  write_future_ = {};  // 重置 future
  mems_tcp_handle_.reset();
}

}  // namespace robosense::lidar
