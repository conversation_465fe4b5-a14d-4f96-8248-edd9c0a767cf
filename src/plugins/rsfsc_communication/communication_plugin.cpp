﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "communication_plugin.h"
#include "bt_node_tcp.h"
#include "node_models/bt_node_model.h"
#include <utility>

namespace robosense::lidar
{

std::string CommunicationPlugin::name() const { return "TCP通讯节点"; }
std::string CommunicationPlugin::version() const { return "1.0.0"; }
bool CommunicationPlugin::registerNodes()
{
  // 注册节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "TcpConnect";
    model.display_name    = "连接";
    model.setInstanceName("TcpConnect");
    PortModels ports;
    // 设置端口信息，包括数据类型
    PortModel tcp_handle_port;
    tcp_handle_port.type_name     = "tcp_handle";
    tcp_handle_port.direction     = BT::PortDirection::OUTPUT;
    tcp_handle_port.description   = "输出端口";
    tcp_handle_port.default_value = "{tcp_handle}";
    tcp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(tcp_handle_port) });

    PortModel ip_addr_port;
    ip_addr_port.type_name     = "ip_address";
    ip_addr_port.direction     = BT::PortDirection::INPUT;
    ip_addr_port.description   = "输入端口";
    ip_addr_port.default_value = "*************";
    ip_addr_port.data_type     = PortDataType::String;
    ports.insert({ "IP地址", std::move(ip_addr_port) });

    PortModel port_port;
    port_port.type_name     = "port";
    port_port.direction     = BT::PortDirection::INPUT;
    port_port.description   = "输入端口";
    port_port.default_value = "6699";
    port_port.data_type     = PortDataType::Integer;
    port_port.min_value     = 1;
    port_port.max_value     = 65535;
    ports.insert({ "端口号", std::move(port_port) });

    PortModel timeout_port;
    timeout_port.type_name     = "timeout_ms";
    timeout_port.direction     = BT::PortDirection::INPUT;
    timeout_port.description   = "输入端口";
    timeout_port.default_value = "5000";
    timeout_port.data_type     = PortDataType::Integer;
    timeout_port.min_value     = 100;
    timeout_port.max_value     = 30000;
    ports.insert({ "超时时间", std::move(timeout_port) });
    model.ports = std::move(ports);
    registerNodeTypeModel<TcpConnect, BtNodeModel>("TcpConnect", "Action", model);
  }

  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "TcpReadReg";
    model.display_name    = "寄存器读取";
    model.setInstanceName("TcpReadReg");
    PortModels ports;
    // 设置端口信息，包括数据类型
    PortModel tcp_handle_port;
    tcp_handle_port.type_name     = "tcp_handle";
    tcp_handle_port.direction     = BT::PortDirection::INPUT;
    tcp_handle_port.description   = "设备句柄";
    tcp_handle_port.default_value = "{tcp_handle}";
    tcp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(tcp_handle_port) });

    PortModel addr_port;
    addr_port.type_name = "reg_addrs";
    addr_port.direction = BT::PortDirection::INPUT;
    addr_port.description = "寄存器地址，支持多种格式：\n1. 逗号分隔列表: 0x63c00100,0x63c00104,0x63c00108\n2. "
                            "范围表示: 0x63c00100-0x63c00120\n3. 混合模式: 0x63c00100,0x63c00104-0x63c00110";
    addr_port.default_value = "0x63c2010c";
    addr_port.data_type     = PortDataType::String;
    addr_port.height_factor = 3;  // 设置地址字段的高度为标准高度的3倍，方便输入更多地址，自动启用多行模式
    ports.insert({ "地址", std::move(addr_port) });

    PortModel timeout_port;
    timeout_port.type_name     = "timeout_ms";
    timeout_port.direction     = BT::PortDirection::INPUT;
    timeout_port.description   = "每个命令发送后的超时时间ms";
    timeout_port.default_value = "100";
    timeout_port.data_type     = PortDataType::Integer;
    timeout_port.min_value     = 10;
    timeout_port.max_value     = 10000;
    ports.insert({ "超时时间", std::move(timeout_port) });

    PortModel read_values_port;
    read_values_port.type_name     = "read_values";
    read_values_port.direction     = BT::PortDirection::OUTPUT;
    read_values_port.description   = "读取值";
    read_values_port.default_value = "{read_values}";
    read_values_port.data_type     = PortDataType::String;
    ports.insert({ "读取值", std::move(read_values_port) });
    model.ports = std::move(ports);
    registerNodeTypeModel<TcpReadReg, BtNodeModel>("TcpReadReg", "Action", model);
  }

  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "TcpWriteReg";
    model.display_name    = "寄存器写入";
    model.setInstanceName("TcpWriteReg");
    PortModels ports;
    // 设置端口信息，包括数据类型
    PortModel tcp_handle_port;
    tcp_handle_port.type_name     = "tcp_handle";
    tcp_handle_port.direction     = BT::PortDirection::INPUT;
    tcp_handle_port.description   = "设备句柄";
    tcp_handle_port.default_value = "{tcp_handle}";
    tcp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(tcp_handle_port) });

    PortModel addr_port;
    addr_port.type_name = "reg_addrs";
    addr_port.direction = BT::PortDirection::INPUT;
    addr_port.description = "寄存器地址，支持多种格式：\n1. 逗号分隔列表: 0x63c00100,0x63c00104,0x63c00108\n2. "
                            "范围表示: 0x63c00100-0x63c00120\n3. 混合模式: 0x63c00100,0x63c00104-0x63c00110";
    addr_port.default_value = "0x63c2010c";
    addr_port.data_type     = PortDataType::String;
    addr_port.height_factor = 3;  // 设置地址字段的高度为标准高度的3倍，方便输入更多地址，自动启用多行模式
    ports.insert({ "地址", std::move(addr_port) });

    PortModel write_value_port;
    write_value_port.type_name = "reg_values";
    write_value_port.direction = BT::PortDirection::INPUT;
    write_value_port.description = "写入的值，如果地址包含多个但只提供一个值，将使用该值填充所有地址";
    write_value_port.default_value = "10";
    write_value_port.data_type     = PortDataType::Integer;
    ports.insert({ "写入值", std::move(write_value_port) });

    PortModel timeout_port;
    timeout_port.type_name     = "timeout_ms";
    timeout_port.direction     = BT::PortDirection::INPUT;
    timeout_port.description   = "每个命令发送后的超时时间ms";
    timeout_port.default_value = "100";
    timeout_port.data_type     = PortDataType::Integer;
    timeout_port.min_value     = 10;
    timeout_port.max_value     = 10000;
    ports.insert({ "超时时间", std::move(timeout_port) });
    model.ports = std::move(ports);
    registerNodeTypeModel<TcpWriteReg, BtNodeModel>("TcpWriteReg", "Action", model);
  }

  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "TcpDisconnect";
    model.display_name    = "断开";
    model.setInstanceName("TcpDisconnect");
    PortModels ports;
    // 设置端口信息，包括数据类型
    PortModel tcp_handle_port;
    tcp_handle_port.type_name     = "tcp_handle";
    tcp_handle_port.direction     = BT::PortDirection::INPUT;
    tcp_handle_port.description   = "设备句柄";
    tcp_handle_port.default_value = "{tcp_handle}";
    tcp_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(tcp_handle_port) });
    model.ports = std::move(ports);
    registerNodeTypeModel<TcpDisconnect, BtNodeModel>("TcpDisconnect", "Action", model);
  }

  return true;
}

}  // namespace robosense::lidar