﻿
/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef BT_NODE_TCP_H
#define BT_NODE_TCP_H

#include "../../common/common_types.h"
#include "mems_tcp.h"
#include <behaviortree_cpp/behavior_tree.h>
#include <behaviortree_cpp/utils/strcat.hpp>
#include <cstdint>
#include <string>
#include <vector>

namespace robosense::lidar
{
// 辅助函数声明
bool strToUint32(const std::string& _str, uint32_t& _out_value);

/**
 * @brief 解析地址字符串，支持多种格式
 *
 * 支持的格式:
 * 1. 逗号分隔列表: "0x83c00100,0x83c00104,0x83c00108"
 * 2. 范围表示: "0x83c00100-0x83c00120"
 * 3. 混合模式: "0x83c00100,0x83c00104-0x83c00110"
 *
 * @param _addr_str 地址字符串
 * @param _out_addresses 解析后的地址列表
 * @return bool 解析是否成功
 */
bool parseAddressString(const std::string& _addr_str, std::vector<uint32_t>& _out_addresses);

// SequenceWithFailureCheck 节点
class SequenceWithFailureCheck : public BT::ControlNode
{
public:
  SequenceWithFailureCheck(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
};

// TCP连接节点
class TcpConnect : public BT::CoroActionNode
{
public:
  TcpConnect(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> connection_future_;  // 跟踪异步连接
};

// TCP断开连接节点
class TcpDisconnect : public BT::StatefulActionNode
{
public:
  TcpDisconnect(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus onStart() override;
  BT::NodeStatus onRunning() override;
  void onHalted() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> disconnect_future_;
};

// TCP读寄存器节点
class TcpReadReg : public BT::CoroActionNode
{
public:
  TcpReadReg(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  using ReadResultFuture = std::future<std::pair<bool, std::vector<int32_t>>>;
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  ReadResultFuture read_future_;
};

// TCP写寄存器节点
class TcpWriteReg : public BT::CoroActionNode
{
public:
  TcpWriteReg(const std::string& _name, const BT::NodeConfig& _config);
  static BT::PortsList providedPorts();
  BT::NodeStatus tick() override;
  void halt() override;

private:
  std::shared_ptr<MEMSTCP> mems_tcp_handle_ { nullptr };
  std::future<bool> write_future_;
};
}  // namespace robosense::lidar

#endif  // BT_NODE_TCP_H