﻿cmake_minimum_required(VERSION 3.10)
set(PLUGIN_NAME rsfsc_communication_plugin)
string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
project(PLUGIN_NAME VERSION 0.1.0.${PROJECT_COMPILE_DATE})

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# Add the nodeeditor library
# include_directories(${CMAKE_SOURCE_DIR}/lib/nodeeditor/include)

# Generate dynamic library
add_library(${PLUGIN_NAME} SHARED)

# Link Qt5 and nodeeditor
target_link_libraries(${PLUGIN_NAME} PRIVATE Qt5::Core Qt5::Widgets common::common)

# Add compile definitions for nodeeditor compatibility
# target_compile_definitions(${PLUGIN_NAME} PRIVATE NODE_EDITOR_STATIC)

target_sources(${PLUGIN_NAME} PRIVATE communication_plugin.h communication_plugin.cpp bt_node_tcp.h bt_node_tcp.cpp)

# Set include directories
target_include_directories(${PLUGIN_NAME} PUBLIC ${CMAKE_SOURCE_DIR}/include ${CMAKE_SOURCE_DIR}
                                                 $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>)

# Set output directory
set_target_properties(${PLUGIN_NAME} PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/plugins
                                                POSITION_INDEPENDENT_CODE ON)

# Install plugin
install(TARGETS ${PLUGIN_NAME} DESTINATION plugins)
