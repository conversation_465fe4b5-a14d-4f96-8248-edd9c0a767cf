﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef LOG_SETTING_PLUGIN_H
#define LOG_SETTING_PLUGIN_H

#include "node_models/node_plugin_base.h"
#include <QtCore/QObject>

namespace robosense::lidar
{

/**
 * @brief rsfsc_lib公共库设置节点插件类
 * 
 * 提供与日志设置和测试结果记录相关的节点
 */
class LogSettingPlugin : public QObject, public NodePluginBase
{
  Q_OBJECT
  Q_INTERFACES(robosense::lidar::NodePluginInterface)
  Q_PLUGIN_METADATA(IID NodePluginInterface_iid)

public:
  LogSettingPlugin()                        = default;
  LogSettingPlugin(const LogSettingPlugin&) = delete;
  LogSettingPlugin& operator=(const LogSettingPlugin&) = delete;
  LogSettingPlugin(LogSettingPlugin&&)                 = delete;
  LogSettingPlugin& operator=(LogSettingPlugin&&) = delete;
  ~LogSettingPlugin() override                    = default;

  /**
   * @brief 获取插件名称
   * @return 插件名称
   */
  [[nodiscard]] std::string name() const override;

  /**
   * @brief 获取插件版本
   * @return 插件版本
   */
  [[nodiscard]] std::string version() const override;

  /**
   * @brief 注册所有节点
   * @return 注册是否成功
   */
  bool registerNodes() override;
};

}  // namespace robosense::lidar

#endif  // LOG_SETTING_PLUGIN_H
