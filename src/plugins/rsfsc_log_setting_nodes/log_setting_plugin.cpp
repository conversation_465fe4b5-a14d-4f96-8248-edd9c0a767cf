﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "log_setting_plugin.h"
#include "log_setting_nodes.h"
#include "node_models/bt_node_model.h"
#include <utility>

namespace robosense::lidar
{

std::string LogSettingPlugin::name() const { return "公共库设置节点"; }
std::string LogSettingPlugin::version() const { return "1.0.0"; }

bool LogSettingPlugin::registerNodes()
{
  // 注册CheckLidarState节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "CheckLidarState";
    model.display_name    = "过站检查";
    model.setInstanceName("CheckLidarState");
    PortModels ports;

    // 输出端口：检查状态
    PortModel check_state_port;
    check_state_port.type_name     = "check_state";
    check_state_port.direction     = BT::PortDirection::OUTPUT;
    check_state_port.description   = "检查结果枚举值";
    check_state_port.default_value = "{check_state}";
    check_state_port.data_type     = PortDataType::Integer;
    ports.insert({ "检查状态", std::move(check_state_port) });

    // 输出端口：数据路径
    PortModel data_path_port;
    data_path_port.type_name     = "data_path";
    data_path_port.direction     = BT::PortDirection::OUTPUT;
    data_path_port.description   = "分配的数据存储路径";
    data_path_port.default_value = "{data_path}";
    data_path_port.data_type     = PortDataType::String;
    ports.insert({ "数据路径", std::move(data_path_port) });

    // 输出端口：结果路径
    PortModel result_path_port;
    result_path_port.type_name     = "result_path";
    result_path_port.direction     = BT::PortDirection::OUTPUT;
    result_path_port.description   = "分配的结果存储路径";
    result_path_port.default_value = "{result_path}";
    result_path_port.data_type     = PortDataType::String;
    ports.insert({ "结果路径", std::move(result_path_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<CheckLidarStateNode, BtNodeModel>("CheckLidarState", "LogSetting", model);
  }

  // 注册GetLidarData节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "GetLidarData";
    model.display_name    = "获取雷达数据";
    model.setInstanceName("GetLidarData");
    PortModels ports;

    // 输出端口：雷达序列号
    PortModel lidar_sn_port;
    lidar_sn_port.type_name     = "lidar_sn";
    lidar_sn_port.direction     = BT::PortDirection::OUTPUT;
    lidar_sn_port.description   = "雷达序列号";
    lidar_sn_port.default_value = "{lidar_sn}";
    lidar_sn_port.data_type     = PortDataType::String;
    ports.insert({ "雷达序列号", std::move(lidar_sn_port) });

    // 输出端口：线缆序列号
    PortModel cable_sn_port;
    cable_sn_port.type_name     = "cable_sn";
    cable_sn_port.direction     = BT::PortDirection::OUTPUT;
    cable_sn_port.description   = "线缆序列号";
    cable_sn_port.default_value = "{cable_sn}";
    cable_sn_port.data_type     = PortDataType::String;
    ports.insert({ "线缆序列号", std::move(cable_sn_port) });

    // 输出端口：线缆使用次数
    PortModel cable_use_times_port;
    cable_use_times_port.type_name     = "cable_use_times";
    cable_use_times_port.direction     = BT::PortDirection::OUTPUT;
    cable_use_times_port.description   = "线缆使用次数";
    cable_use_times_port.default_value = "{cable_use_times}";
    cable_use_times_port.data_type     = PortDataType::Integer;
    ports.insert({ "线缆使用次数", std::move(cable_use_times_port) });

    // 输出端口：项目代码字符串
    PortModel project_code_str_port;
    project_code_str_port.type_name     = "project_code_str";
    project_code_str_port.direction     = BT::PortDirection::OUTPUT;
    project_code_str_port.description   = "项目代码字符串";
    project_code_str_port.default_value = "{project_code_str}";
    project_code_str_port.data_type     = PortDataType::String;
    ports.insert({ "项目代码", std::move(project_code_str_port) });

    // 输出端口：项目代码索引
    PortModel project_code_index_port;
    project_code_index_port.type_name     = "project_code_index";
    project_code_index_port.direction     = BT::PortDirection::OUTPUT;
    project_code_index_port.description   = "项目代码索引";
    project_code_index_port.default_value = "{project_code_index}";
    project_code_index_port.data_type     = PortDataType::Integer;
    ports.insert({ "项目代码索引", std::move(project_code_index_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<GetLidarDataNode, BtNodeModel>("GetLidarData", "LogSetting", model);
  }

  // 注册AddNumericMeasure节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "AddNumericMeasure";
    model.display_name    = "添加数值测量";
    model.setInstanceName("AddNumericMeasure");
    PortModels ports;

    // 输入端口：标签
    PortModel label_port;
    label_port.type_name     = "label";
    label_port.direction     = BT::PortDirection::INPUT;
    label_port.description   = "测量项的标签";
    label_port.default_value = "测量项";
    label_port.data_type     = PortDataType::String;
    ports.insert({ "标签", std::move(label_port) });

    // 输入端口：下限
    PortModel lower_limit_port;
    lower_limit_port.type_name     = "lower_limit";
    lower_limit_port.direction     = BT::PortDirection::INPUT;
    lower_limit_port.description   = "允许的下限";
    lower_limit_port.default_value = "0.0";
    lower_limit_port.data_type     = PortDataType::Double;
    ports.insert({ "下限", std::move(lower_limit_port) });

    // 输入端口：上限
    PortModel upper_limit_port;
    upper_limit_port.type_name     = "upper_limit";
    upper_limit_port.direction     = BT::PortDirection::INPUT;
    upper_limit_port.description   = "允许的上限";
    upper_limit_port.default_value = "100.0";
    upper_limit_port.data_type     = PortDataType::Double;
    ports.insert({ "上限", std::move(upper_limit_port) });

    // 输入端口：单位
    PortModel unit_port;
    unit_port.type_name     = "unit";
    unit_port.direction     = BT::PortDirection::INPUT;
    unit_port.description   = "测量值的单位";
    unit_port.default_value = "mm";
    unit_port.data_type     = PortDataType::String;
    ports.insert({ "单位", std::move(unit_port) });

    // 输入端口：数值
    PortModel data_value_port;
    data_value_port.type_name     = "data_value";
    data_value_port.direction     = BT::PortDirection::INPUT;
    data_value_port.description   = "实际测量的数值（如果未提供变量名则使用此值）";
    data_value_port.default_value = "0.0";
    data_value_port.data_type     = PortDataType::Double;
    ports.insert({ "数值", std::move(data_value_port) });

    // 输入端口：变量名
    PortModel variable_name_port;
    variable_name_port.type_name     = "variable_name";
    variable_name_port.direction     = BT::PortDirection::INPUT;
    variable_name_port.description   = "要从黑板获取的变量名称，如果提供则优先使用";
    variable_name_port.default_value = "";
    variable_name_port.data_type     = PortDataType::String;
    ports.insert({ "变量名", std::move(variable_name_port) });

    // 输入端口：数据类型
    PortModel data_type_port;
    data_type_port.type_name     = "data_type";
    data_type_port.direction     = BT::PortDirection::INPUT;
    data_type_port.description   = "数据的类型 (0=浮点数, 1=十六进制, 2=整数)";
    data_type_port.default_value = "0";
    data_type_port.data_type     = PortDataType::Integer;
    data_type_port.min_value     = 0;
    data_type_port.max_value     = 2;
    ports.insert({ "数据类型", std::move(data_type_port) });

    // 输出端口：是否在限值范围内
    PortModel is_within_limit_port;
    is_within_limit_port.type_name     = "is_within_limit";
    is_within_limit_port.direction     = BT::PortDirection::OUTPUT;
    is_within_limit_port.description   = "测量值是否在限值范围内";
    is_within_limit_port.default_value = "{is_within_limit}";
    is_within_limit_port.data_type     = PortDataType::Boolean;
    ports.insert({ "是否在范围内", std::move(is_within_limit_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<AddNumericMeasureNode, BtNodeModel>("AddNumericMeasure", "LogSetting", model);
  }

  // 注册AddStringMeasure节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "AddStringMeasure";
    model.display_name    = "添加字符串测量";
    model.setInstanceName("AddStringMeasure");
    PortModels ports;

    // 输入端口：标签
    PortModel label_port;
    label_port.type_name     = "label";
    label_port.direction     = BT::PortDirection::INPUT;
    label_port.description   = "测量项的标签";
    label_port.default_value = "测量项";
    label_port.data_type     = PortDataType::String;
    ports.insert({ "标签", std::move(label_port) });

    // 输入端口：期望值
    PortModel expected_value_port;
    expected_value_port.type_name     = "expected_value";
    expected_value_port.direction     = BT::PortDirection::INPUT;
    expected_value_port.description   = "期望的字符串值";
    expected_value_port.default_value = "";
    expected_value_port.data_type     = PortDataType::String;
    ports.insert({ "期望值", std::move(expected_value_port) });

    // 输入端口：实际值
    PortModel actual_value_port;
    actual_value_port.type_name     = "actual_value";
    actual_value_port.direction     = BT::PortDirection::INPUT;
    actual_value_port.description   = "实际测量的字符串值（如果未提供变量名则使用此值）";
    actual_value_port.default_value = "";
    actual_value_port.data_type     = PortDataType::String;
    ports.insert({ "实际值", std::move(actual_value_port) });

    // 输入端口：变量名
    PortModel variable_name_port;
    variable_name_port.type_name     = "variable_name";
    variable_name_port.direction     = BT::PortDirection::INPUT;
    variable_name_port.description   = "要从黑板获取的变量名称，如果提供则优先使用";
    variable_name_port.default_value = "";
    variable_name_port.data_type     = PortDataType::String;
    ports.insert({ "变量名", std::move(variable_name_port) });

    // 输出端口：是否匹配
    PortModel is_match_port;
    is_match_port.type_name     = "is_match";
    is_match_port.direction     = BT::PortDirection::OUTPUT;
    is_match_port.description   = "实际值是否与期望值相同";
    is_match_port.default_value = "{is_match}";
    is_match_port.data_type     = PortDataType::Boolean;
    ports.insert({ "是否匹配", std::move(is_match_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<AddStringMeasureNode, BtNodeModel>("AddStringMeasure", "LogSetting", model);
  }

  // 注册SetTestStatus节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "SetTestStatus";
    model.display_name    = "设置测试状态";
    model.setInstanceName("SetTestStatus");
    PortModels ports;

    // 输入端口：测试状态
    PortModel test_status_port;
    test_status_port.type_name     = "test_status";
    test_status_port.direction     = BT::PortDirection::INPUT;
    test_status_port.description   = "要设置的测试状态枚举值 (0=未准备好, 1=通过, 2=失败)";
    test_status_port.default_value = "1";
    test_status_port.data_type     = PortDataType::Integer;
    test_status_port.min_value     = 0;
    test_status_port.max_value     = 2;
    ports.insert({ "测试状态", std::move(test_status_port) });

    // 输入端口：失败标签
    PortModel fail_label_port;
    fail_label_port.type_name     = "fail_label";
    fail_label_port.direction     = BT::PortDirection::INPUT;
    fail_label_port.description   = "如果test_status不是Pass，则提供简短失败标签";
    fail_label_port.default_value = "";
    fail_label_port.data_type     = PortDataType::String;
    ports.insert({ "失败标签", std::move(fail_label_port) });

    // 输入端口：失败信息
    PortModel fail_msg_port;
    fail_msg_port.type_name     = "fail_msg";
    fail_msg_port.direction     = BT::PortDirection::INPUT;
    fail_msg_port.description   = "如果test_status不是Pass，则提供详细失败信息";
    fail_msg_port.default_value = "";
    fail_msg_port.data_type     = PortDataType::String;
    fail_msg_port.height_factor = 2;  // 设置为多行文本
    ports.insert({ "失败信息", std::move(fail_msg_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<SetTestStatusNode, BtNodeModel>("SetTestStatus", "LogSetting", model);
  }

  // 注册FinishLidarProcess节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "FinishLidarProcess";
    model.display_name    = "测试结束上传";
    model.setInstanceName("FinishLidarProcess");
    PortModels ports;

    // 输出端口：错误信息
    PortModel error_msg_port;
    error_msg_port.type_name     = "error_msg";
    error_msg_port.direction     = BT::PortDirection::OUTPUT;
    error_msg_port.description   = "如果完成过程中出现错误，这里会包含错误信息";
    error_msg_port.default_value = "{error_msg}";
    error_msg_port.data_type     = PortDataType::String;
    ports.insert({ "错误信息", std::move(error_msg_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<FinishLidarProcessNode, BtNodeModel>("FinishLidarProcess", "LogSetting", model);
  }

  // 注册SetLidarConfig节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "SetLidarConfig";
    model.display_name    = "设置雷达配置";
    model.setInstanceName("SetLidarConfig");
    PortModels ports;

    // 输入端口：是否设置固定项目代码
    PortModel use_fixed_project_code_port;
    use_fixed_project_code_port.type_name     = "use_fixed_project_code";
    use_fixed_project_code_port.direction     = BT::PortDirection::INPUT;
    use_fixed_project_code_port.description   = "是否设置固定项目代码";
    use_fixed_project_code_port.default_value = "false";
    use_fixed_project_code_port.data_type     = PortDataType::Boolean;
    ports.insert({ "使用固定项目代码", std::move(use_fixed_project_code_port) });

    // 输入端口：固定项目代码
    PortModel fixed_project_code_port;
    fixed_project_code_port.type_name     = "fixed_project_code";
    fixed_project_code_port.direction     = BT::PortDirection::INPUT;
    fixed_project_code_port.description   = "如果use_fixed_project_code为true，则设置此值";
    fixed_project_code_port.default_value = "0";
    fixed_project_code_port.data_type     = PortDataType::Integer;
    fixed_project_code_port.min_value     = 0;
    fixed_project_code_port.max_value     = 10;
    ports.insert({ "固定项目代码", std::move(fixed_project_code_port) });

    // 输入端口：是否设置固定安装位置
    PortModel use_fixed_lidar_position_port;
    use_fixed_lidar_position_port.type_name     = "use_fixed_lidar_position";
    use_fixed_lidar_position_port.direction     = BT::PortDirection::INPUT;
    use_fixed_lidar_position_port.description   = "是否设置固定安装位置";
    use_fixed_lidar_position_port.default_value = "false";
    use_fixed_lidar_position_port.data_type     = PortDataType::Boolean;
    ports.insert({ "使用固定安装位置", std::move(use_fixed_lidar_position_port) });

    // 输入端口：固定安装位置
    PortModel fixed_lidar_position_port;
    fixed_lidar_position_port.type_name     = "fixed_lidar_position";
    fixed_lidar_position_port.direction     = BT::PortDirection::INPUT;
    fixed_lidar_position_port.description   = "如果use_fixed_lidar_position_为true，则设置此值";
    fixed_lidar_position_port.default_value = "0";
    fixed_lidar_position_port.data_type     = PortDataType::Integer;
    fixed_lidar_position_port.min_value     = 0;
    fixed_lidar_position_port.max_value     = 10;
    ports.insert({ "固定安装位置", std::move(fixed_lidar_position_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<SetLidarConfigNode, BtNodeModel>("SetLidarConfig", "LogSetting", model);
  }

  return true;
}

}  // namespace robosense::lidar
