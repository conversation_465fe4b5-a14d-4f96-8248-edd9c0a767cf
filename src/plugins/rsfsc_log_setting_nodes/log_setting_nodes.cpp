﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "log_setting_nodes.h"
#include "include/csv_parser.h"
#include "include/rsfsc_msg.h"
#include "include/widget_lidar_info.h"
#include "include/widget_log_setting.h"
#include <rsfsc_log/rsfsc_log_macro.h>
#include <unordered_map>

namespace robosense::lidar
{

namespace
{
// 使用完整的命名空间
using WidgetLogSetting     = robosense::lidar::rsfsc_lib::WidgetLogSetting;
using WidgetLidarInfo      = robosense::lidar::rsfsc_lib::WidgetLidarInfo;
using CheckState           = robosense::lidar::rsfsc_lib::CheckState;
using LogTestStatus        = robosense::lidar::rsfsc_lib::LogTestStatus;
using MeasureDataType      = robosense::lidar::rsfsc_lib::MeasureDataType;
using LimitInfo            = robosense::lidar::LimitInfo;
using ProjectCode          = robosense::lidar::rsfsc_lib::ProjectCode;
using LidarInstallPosition = robosense::lidar::rsfsc_lib::LidarInstallPosition;

// 从黑板中获取当前Lidar索引
int getCurrentLidarIndex(const BT::Blackboard::Ptr& _blackboard)
{
  int index = 0;
  if (!_blackboard->get("current_lidar_index", index))
  {
    LOG_ERROR("Failed to get current_lidar_index from blackboard");
    return 0;
  }
  return index;
}

// 从黑板中获取全局日志设置实例
WidgetLogSetting* getGlobalLogSetting(const BT::Blackboard::Ptr& _blackboard)
{
  auto* log_setting = _blackboard->get<WidgetLogSetting*>("g_widget_log_setting");
  if (log_setting == nullptr)
  {
    LOG_ERROR("Failed to get g_widget_log_setting from blackboard");
    return nullptr;
  }
  return log_setting;
}

// 从黑板中获取Lidar控件列表
std::unordered_map<int, WidgetLidarInfo*>* getLidarWidgets(const BT::Blackboard::Ptr& _blackboard)
{
  auto* widgets = _blackboard->get<std::unordered_map<int, WidgetLidarInfo*>*>("g_lidar_info_widgets");
  if (widgets == nullptr)
  {
    LOG_ERROR("Failed to get g_lidar_info_widgets from blackboard");
    return nullptr;
  }
  return widgets;
}
}  // namespace

//==============================================================================
// CheckLidarStateNode 实现
//==============================================================================
CheckLidarStateNode::CheckLidarStateNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList CheckLidarStateNode::providedPorts()
{
  return { BT::OutputPort<robosense::lidar::rsfsc_lib::CheckState>("check_state", "检查结果枚举值"),
           BT::OutputPort<QString>("data_path", "分配的数据存储路径"),
           BT::OutputPort<QString>("result_path", "分配的结果存储路径") };
}

BT::NodeStatus CheckLidarStateNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和global_log_setting_
  auto blackboard             = this->config().blackboard;
  quint32 current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* global_log_setting    = getGlobalLogSetting(blackboard);

  // 2. 检查global_log_setting_是否有效
  if (global_log_setting == nullptr || current_lidar_index == 0)
  {
    LOG_ERROR("Invalid g_widget_log_setting or current_lidar_index");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 声明三个QString变量data_path, temp_path, result_path
  QString data_path;
  QString temp_path;
  QString result_path;

  // 4. 调用global_log_setting_->checkAllState
  auto check_state = global_log_setting->checkAllState(current_lidar_index, data_path, temp_path, result_path);

  LOG_DEBUG("check_state = {}, data_path = {}, result_path = {}", check_state, data_path, result_path);

  // 5. 将返回的CheckState存储到输出check_state_
  setOutput("check_state", check_state);

  // 6. 将获取到的data_path和result_path存储到对应的输出
  setOutput("data_path", data_path);
  setOutput("result_path", result_path);

  // 7. 如果返回的CheckState是CHECK_STATE_SUCCESS，则节点返回Success
  if (check_state == robosense::lidar::rsfsc_lib::CHECK_STATE_SUCCESS)
  {
    LOG_INFO("CheckLidarStateNode: Check state success");
    return BT::NodeStatus::SUCCESS;
  }

  // 8. 否则，节点返回Failure
  LOG_ERROR("CheckLidarStateNode: Check state failed with code: {}", static_cast<int>(check_state));
  return BT::NodeStatus::FAILURE;
}

//==============================================================================
// GetLidarDataNode 实现
//==============================================================================
GetLidarDataNode::GetLidarDataNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList GetLidarDataNode::providedPorts()
{
  return { BT::OutputPort<QString>("lidar_sn", "雷达序列号"),
           BT::OutputPort<QString>("cable_sn", "线缆序列号"),
           BT::OutputPort<quint32>("cable_use_times", "线缆使用次数"),
           BT::OutputPort<QString>("project_code_str", "项目代码字符串"),
           BT::OutputPort<robosense::lidar::rsfsc_lib::ProjectCode>("project_code_index", "项目代码索引"),
           BT::OutputPort<QString>("car_type", "车型"),
           BT::OutputPort<robosense::lidar::rsfsc_lib::LidarInstallPosition>("lidar_install_position", "雷达安装位置"),
           BT::OutputPort<QString>("ip_address", "IP地址"),
           BT::OutputPort<quint16>("msop_port", "MSOP端口"),
           BT::OutputPort<quint16>("difop_port", "DIFOP端口"),
           BT::OutputPort<QString>("pcap_path", "PCAP文件路径"),
           BT::OutputPort<QString>("param_path", "参数文件路径"),
           BT::OutputPort<QString>("additional_info", "附加信息") };
}

BT::NodeStatus GetLidarDataNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和lidar_widgets_
  auto blackboard         = this->config().blackboard;
  int current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* lidar_widgets     = getLidarWidgets(blackboard);

  // 2. 检查lidar_widgets_是否有效，以及current_lidar_index_是否在lidar_widgets_的有效范围内
  if (lidar_widgets == nullptr || current_lidar_index == 0 ||
      current_lidar_index > static_cast<int>(lidar_widgets->size()))
  {
    LOG_ERROR("Invalid g_lidar_info_widgets or current_lidar_index out of range");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 获取对应的WidgetLidarInfo*
  auto* lidar_info = (*lidar_widgets)[current_lidar_index];

  // 4. 检查lidar_info是否为nullptr
  if (lidar_info == nullptr)
  {
    LOG_ERROR("lidar_info is nullptr");
    return BT::NodeStatus::FAILURE;
  }

  // 5. 依次调用lidar_info的各个get...()方法
  // 6. 将获取到的值赋给对应的节点输出端口
  setOutput("lidar_sn", lidar_info->getLidarSN());
  setOutput("cable_sn", lidar_info->getCableSN());
  setOutput("cable_use_times", lidar_info->getCableUseTimes());
  setOutput("project_code_str", lidar_info->getProjectCodeStr());
  setOutput("project_code_index", lidar_info->getProjectCodeIndex());
  setOutput("car_type", lidar_info->getCarType());
  setOutput("lidar_install_position", lidar_info->getLidarInstallPosition());
  setOutput("ip_address", lidar_info->getIP());
  setOutput("msop_port", lidar_info->getMSOPPort());
  setOutput("difop_port", lidar_info->getDIFOPPort());
  setOutput("pcap_path", lidar_info->getPcapPath());
  setOutput("param_path", lidar_info->getParamPath());
  setOutput("additional_info", lidar_info->getAdditionalInfo());

  // 7. 节点返回Success
  return BT::NodeStatus::SUCCESS;
}

//==============================================================================
// AddNumericMeasureNode 实现
//==============================================================================
AddNumericMeasureNode::AddNumericMeasureNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList AddNumericMeasureNode::providedPorts()
{
  return { BT::InputPort<std::string>("label", "测量项的标签"),
           BT::InputPort<double>("lower_limit", "允许的下限"),
           BT::InputPort<double>("upper_limit", "允许的上限"),
           BT::InputPort<std::string>("unit", "测量值的单位"),
           BT::InputPort<double>("data_value", "实际测量的数值"),
           BT::InputPort<std::string>("variable_name", "要从黑板获取的变量名称，如果提供则优先使用"),
           BT::InputPort<robosense::lidar::rsfsc_lib::MeasureDataType>(
             "data_type", robosense::lidar::rsfsc_lib::MEASURE_DATA_TYPE_FLOAT, "数据的类型"),
           BT::OutputPort<bool>("is_within_limit", "测量值是否在限值范围内") };
}

BT::NodeStatus AddNumericMeasureNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和global_log_setting_
  auto blackboard             = this->config().blackboard;
  quint32 current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* global_log_setting    = getGlobalLogSetting(blackboard);

  // 2. 检查global_log_setting_是否有效
  if (global_log_setting == nullptr || current_lidar_index == 0)
  {
    LOG_ERROR("Invalid g_widget_log_setting or current_lidar_index");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 从节点输入获取label_, lower_limit_, upper_limit_, unit_, data_value_, data_type_
  auto label_result         = getInput<std::string>("label");
  auto lower_limit_result   = getInput<double>("lower_limit");
  auto upper_limit_result   = getInput<double>("upper_limit");
  auto unit_result          = getInput<std::string>("unit");
  auto variable_name_result = getInput<std::string>("variable_name");
  auto data_type_result     = getInput<robosense::lidar::rsfsc_lib::MeasureDataType>("data_type");

  if (!label_result || !lower_limit_result || !upper_limit_result || !unit_result)
  {
    LOG_ERROR("Missing required input ports");
    return BT::NodeStatus::FAILURE;
  }

  const std::string& label = label_result.value();
  double lower_limit       = lower_limit_result.value();
  double upper_limit       = upper_limit_result.value();
  const std::string& unit  = unit_result.value();
  auto data_type           = data_type_result.value_or(robosense::lidar::rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);

  // 获取数据值，优先从变量名获取
  double data_value = 0.0;
  if (variable_name_result.has_value() && !variable_name_result.value().empty())
  {
    // 从黑板获取变量值
    const std::string& variable_name = variable_name_result.value();
    if (!blackboard->get(variable_name, data_value))
    {
      LOG_ERROR("Failed to get variable '{}' from blackboard", variable_name);
      return BT::NodeStatus::FAILURE;
    }
    LOG_DEBUG("Got value {} from blackboard variable '{}'", data_value, variable_name);
  }
  else
  {
    // 直接从data_value输入端口获取
    auto data_value_result = getInput<double>("data_value");
    if (!data_value_result)
    {
      LOG_ERROR("Missing required input port 'data_value' and no valid 'variable_name' provided");
      return BT::NodeStatus::FAILURE;
    }
    data_value = data_value_result.value();
  }

  // 4. 创建LimitInfo结构体实例limit_info
  robosense::lidar::LimitInfo limit_info;
  limit_info.name   = label;
  limit_info.min_th = lower_limit;
  limit_info.max_th = upper_limit;
  limit_info.unit   = unit;
  limit_info.extra_str_info.emplace_back("测试验证使用");

  // 5. 调用global_log_setting_->addMeasureMessage
  bool is_within_limit = global_log_setting->addMeasureMessage(current_lidar_index, limit_info, data_value, data_type);

  // 6. 将返回的bool值存储到输出is_within_limit_
  setOutput("is_within_limit", is_within_limit);

  // 7. 节点返回Success
  return is_within_limit ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
}

//==============================================================================
// AddStringMeasureNode 实现
//==============================================================================
AddStringMeasureNode::AddStringMeasureNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList AddStringMeasureNode::providedPorts()
{
  return { BT::InputPort<std::string>("label", "测量项的标签"),
           BT::InputPort<std::string>("expected_value", "期望的字符串值"),
           BT::InputPort<std::string>("actual_value", "实际测量的字符串值"),
           BT::InputPort<std::string>("variable_name", "要从黑板获取的变量名称，如果提供则优先使用"),
           BT::OutputPort<bool>("is_match", "实际值是否与期望值相同") };
}

BT::NodeStatus AddStringMeasureNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和global_log_setting_
  auto blackboard             = this->config().blackboard;
  quint32 current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* global_log_setting    = getGlobalLogSetting(blackboard);

  // 2. 检查global_log_setting_是否有效
  if (global_log_setting == nullptr || current_lidar_index == 0)
  {
    LOG_ERROR("Invalid g_widget_log_setting or current_lidar_index");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 从节点输入获取label_, expected_value_, actual_value_
  auto label_result          = getInput<std::string>("label");
  auto expected_value_result = getInput<std::string>("expected_value");
  auto variable_name_result  = getInput<std::string>("variable_name");

  if (!label_result || !expected_value_result)
  {
    LOG_ERROR("Missing required input ports");
    return BT::NodeStatus::FAILURE;
  }

  const std::string& label          = label_result.value();
  const std::string& expected_value = expected_value_result.value();

  // 获取实际值，优先从变量名获取
  std::string actual_value;
  if (variable_name_result.has_value() && !variable_name_result.value().empty())
  {
    // 从黑板获取变量值
    const std::string& variable_name = variable_name_result.value();
    if (!blackboard->get(variable_name, actual_value))
    {
      LOG_ERROR("Failed to get variable '{}' from blackboard", variable_name);
      return BT::NodeStatus::FAILURE;
    }
    LOG_DEBUG("Got value '{}' from blackboard variable '{}'", actual_value, variable_name);
  }
  else
  {
    // 直接从actual_value输入端口获取
    auto actual_value_result = getInput<std::string>("actual_value");
    if (!actual_value_result)
    {
      LOG_ERROR("Missing required input port 'actual_value' and no valid 'variable_name' provided");
      return BT::NodeStatus::FAILURE;
    }
    actual_value = actual_value_result.value();
  }

  // 4. 创建LimitInfo结构体实例
  LimitInfo limit_info;
  limit_info.name        = label;
  limit_info.min_th_text = expected_value;
  limit_info.max_th_text = expected_value;

  // 5. 调用global_log_setting_->addMeasureMessage
  bool is_match = global_log_setting->addMeasureMessage(current_lidar_index, limit_info, actual_value);

  // 6. 将返回的bool值存储到输出is_match_
  setOutput("is_match", is_match);

  // 7. 节点返回Success
  return BT::NodeStatus::SUCCESS;
}

//==============================================================================
// SetTestStatusNode 实现
//==============================================================================
SetTestStatusNode::SetTestStatusNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList SetTestStatusNode::providedPorts()
{
  return { BT::InputPort<int>("test_status", "要设置的测试状态枚举值"),
           BT::InputPort<std::string>("fail_label", "如果test_status_不是Pass，则提供简短失败标签"),
           BT::InputPort<std::string>("fail_msg", "如果test_status_不是Pass，则提供详细失败信息") };
}

BT::NodeStatus SetTestStatusNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和global_log_setting_
  auto blackboard             = this->config().blackboard;
  quint32 current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* global_log_setting    = getGlobalLogSetting(blackboard);

  // 2. 检查global_log_setting_是否有效
  if (global_log_setting == nullptr || current_lidar_index == 0)
  {
    LOG_ERROR("Invalid g_widget_log_setting or current_lidar_index");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 从节点输入获取test_status_, fail_label_, fail_msg_
  auto test_status_result = getInput<int>("test_status");
  auto fail_label_result  = getInput<std::string>("fail_label");
  auto fail_msg_result    = getInput<std::string>("fail_msg");

  if (!test_status_result)
  {
    LOG_ERROR("Missing required input port 'test_status_'");
    return BT::NodeStatus::FAILURE;
  }

  auto test_status_int = test_status_result.value();
  auto test_status     = static_cast<LogTestStatus>(test_status_int);
  QString fail_label = fail_label_result.has_value() ? QString::fromStdString(fail_label_result.value()) : QString("");
  QString fail_msg   = fail_msg_result.has_value() ? QString::fromStdString(fail_msg_result.value()) : QString("");

  // 4. 调用global_log_setting_->setTestStatus
  global_log_setting->setTestStatus(current_lidar_index, test_status, fail_label, fail_msg);

  // 5. 节点返回Success
  return BT::NodeStatus::SUCCESS;
}

//==============================================================================
// FinishLidarProcessNode 实现
//==============================================================================
FinishLidarProcessNode::FinishLidarProcessNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList FinishLidarProcessNode::providedPorts()
{
  return { BT::OutputPort<QString>("error_msg", "如果完成过程中出现错误，这里会包含错误信息") };
}

BT::NodeStatus FinishLidarProcessNode::tick()
{
  // 1. 从上下文中获取current_lidar_index和global_log_setting
  auto blackboard             = this->config().blackboard;
  quint32 current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* global_log_setting    = getGlobalLogSetting(blackboard);

  // 2. 检查global_log_setting_是否有效
  if (global_log_setting == nullptr || current_lidar_index == 0)
  {
    LOG_ERROR("Invalid g_widget_log_setting or current_lidar_index");
    return BT::NodeStatus::FAILURE;
  }

  global_log_setting->setFirmwareRevision(current_lidar_index, 0x00, 0x00);

  // 3. 声明一个QString error_msg
  QString error_msg;

  // 4. 调用global_log_setting_->finishProcess
  bool success = global_log_setting->finishProcess(current_lidar_index, error_msg);

  // 5. 将error_msg存储到输出error_msg_
  setOutput("error_msg", error_msg);

  // 6. 如果finishProcess返回true，节点返回Success
  if (success)
  {
    LOG_INFO("FinishLidarProcessNode: Process finished successfully");
    return BT::NodeStatus::SUCCESS;
  }

  // 7. 否则，节点返回Failure
  LOG_ERROR("FinishLidarProcessNode: Process failed with error: {}", error_msg.toStdString());
  return BT::NodeStatus::FAILURE;
}

//==============================================================================
// SetLidarConfigNode 实现
//==============================================================================
SetLidarConfigNode::SetLidarConfigNode(const std::string& _name, const BT::NodeConfig& _config) :
  BT::SyncActionNode(_name, _config)
{}

BT::PortsList SetLidarConfigNode::providedPorts()
{
  return { BT::InputPort<bool>("use_fixed_project_code", "是否设置固定项目代码"),
           BT::InputPort<int>("fixed_project_code", "如果use_fixed_project_code_为true，则设置此值"),
           BT::InputPort<bool>("use_fixed_lidar_position", "是否设置固定安装位置"),
           BT::InputPort<int>("fixed_lidar_position", "如果use_fixed_lidar_position_为true，则设置此值"),
           BT::InputPort<bool>("use_cable_times", "是否设置线缆使用次数"),
           BT::InputPort<quint32>("cable_use_times", "如果use_cable_times_为true，则设置此值"),
           BT::InputPort<bool>("use_cable_limit", "是否设置线缆次数限制"),
           BT::InputPort<quint32>("cable_use_times_limit", "如果use_cable_limit_为true，则设置此值"),
           BT::InputPort<int>("set_ip_port_mode", "是否以及如何设置IP和端口 (0=None, 1=Default, 2=ByIndex)") };
}

BT::NodeStatus SetLidarConfigNode::tick()
{
  // 1. 从上下文中获取current_lidar_index_和lidar_widgets_
  auto blackboard         = this->config().blackboard;
  int current_lidar_index = getCurrentLidarIndex(blackboard);
  auto* lidar_widgets     = getLidarWidgets(blackboard);

  // 2. 检查lidar_widgets_和索引有效性
  if (lidar_widgets == nullptr || current_lidar_index == 0 ||
      current_lidar_index > static_cast<int>(lidar_widgets->size()))
  {
    LOG_ERROR("Invalid g_lidar_info_widgets or current_lidar_index out of range");
    return BT::NodeStatus::FAILURE;
  }

  // 3. 获取WidgetLidarInfo*
  auto* lidar_info = (*lidar_widgets)[current_lidar_index];
  if (lidar_info == nullptr)
  {
    LOG_ERROR("lidar_info is nullptr");
    return BT::NodeStatus::FAILURE;
  }

  // 4. 根据节点的输入属性，判断需要调用哪些lidar_info的set...()方法
  // 4.1 如果use_fixed_project_code_为true，调用lidar_info->setFixedProjectCode
  auto use_fixed_project_code_result = getInput<bool>("use_fixed_project_code_");
  if (use_fixed_project_code_result.has_value() && use_fixed_project_code_result.value())
  {
    auto fixed_project_code_result = getInput<int>("fixed_project_code_");
    if (fixed_project_code_result.has_value())
    {
      auto project_code = static_cast<robosense::lidar::rsfsc_lib::ProjectCode>(fixed_project_code_result.value());
      lidar_info->setProjectCode(project_code);
      LOG_INFO("SetLidarConfigNode: Set fixed project code to {}", fixed_project_code_result.value());
    }
  }

  // 4.2 如果use_fixed_lidar_position_为true，调用lidar_info->setFixedLidarInstallPosition
  auto use_fixed_lidar_position_result = getInput<bool>("use_fixed_lidar_position_");
  if (use_fixed_lidar_position_result.has_value() && use_fixed_lidar_position_result.value())
  {
    auto fixed_lidar_position_result = getInput<int>("fixed_lidar_position_");
    if (fixed_lidar_position_result.has_value())
    {
      auto lidar_position =
        static_cast<robosense::lidar::rsfsc_lib::LidarInstallPosition>(fixed_lidar_position_result.value());
      lidar_info->setLidarInstallPosition(lidar_position);
      LOG_INFO("SetLidarConfigNode: Set fixed lidar position to {}", fixed_lidar_position_result.value());
    }
  }

  // 4.3 如果use_cable_times_为true，调用lidar_info->setCableUseTimes
  auto use_cable_times_result = getInput<bool>("use_cable_times_");
  if (use_cable_times_result.has_value() && use_cable_times_result.value())
  {
    auto cable_use_times_result = getInput<quint32>("cable_use_times_");
    if (cable_use_times_result.has_value())
    {
      lidar_info->setCableUseTimes(cable_use_times_result.value());
      LOG_INFO("SetLidarConfigNode: Set cable use times to {}", cable_use_times_result.value());
    }
  }

  // 4.4 如果use_cable_limit_为true，调用lidar_info->setCableUseTimesLimit
  auto use_cable_limit_result = getInput<bool>("use_cable_limit_");
  if (use_cable_limit_result.has_value() && use_cable_limit_result.value())
  {
    auto cable_use_times_limit_result = getInput<quint32>("cable_use_times_limit_");
    if (cable_use_times_limit_result.has_value())
    {
      lidar_info->setCableUseTimesLimit(cable_use_times_limit_result.value());
      LOG_INFO("SetLidarConfigNode: Set cable use times limit to {}", cable_use_times_limit_result.value());
    }
  }

  // 4.5 根据set_ip_port_mode_设置IP和端口
  auto set_ip_port_mode_result = getInput<int>("set_ip_port_mode_");
  if (set_ip_port_mode_result.has_value())
  {
    int mode = set_ip_port_mode_result.value();
    if (mode == 1)  // Default
    {
      // 假设这个方法存在，如果不存在，需要根据实际情况调整
      // lidar_info->resetIPandPort();
      LOG_INFO("SetLidarConfigNode: Reset IP and port to default");
    }
    else if (mode == 2)  // ByIndex
    {
      // 假设这个方法存在，如果不存在，需要根据实际情况调整
      // lidar_info->setIPandPortAccording2Index();
      LOG_INFO("SetLidarConfigNode: Set IP and port according to index");
    }
  }

  // 5. 节点返回Success
  return BT::NodeStatus::SUCCESS;
}

}  // namespace robosense::lidar
