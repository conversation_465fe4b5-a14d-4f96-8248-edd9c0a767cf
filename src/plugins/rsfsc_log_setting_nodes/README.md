﻿# 日志设置节点插件

## 简介

日志设置节点插件提供了一组BehaviorTree节点，用于与WidgetLogSetting和WidgetLidarInfo组件交互，实现测试数据的记录和处理。

## 节点列表

### 1. CheckLidarStateNode

检查雷达状态，调用WidgetLogSetting::checkAllState来验证过站。

### 2. GetLidarDataNode

从与当前lidar_index对应的WidgetLidarInfo实例的UI控件中获取用户输入或配置的数据。

### 3. AddNumericMeasureNode

将一个带有上下限的数值测量结果记录到WidgetLogSetting。

### 4. AddStringMeasureNode

将一个字符串类型的测量结果记录到WidgetLogSetting。

### 5. SetTestStatusNode

设置当前Lidar的最终测试状态（通过、失败、未准备好等）。

### 6. FinishLidarProcessNode

标志着当前Lidar的所有处理流程结束，触发日志文件的最终写入和可能的资源释放。

### 7. SetLidarConfigNode

在流程中动态地设置WidgetLidarInfo的某些配置项。

## 使用方法

### AddNumericMeasureNode

该节点用于添加数值类型的测量结果，支持三种数据类型：浮点数、十六进制和整数。

#### 输入端口

- `label`: 测量项的标签
- `lower_limit`: 允许的下限
- `upper_limit`: 允许的上限
- `unit`: 测量值的单位
- `data_value`: 实际测量的数值
- `variable_name`: 要从黑板获取的变量名称，如果提供则优先使用
- `data_type`: 数据的类型，默认为MEASURE_DATA_TYPE_FLOAT

#### 输出端口

- `is_within_limit`: 测量值是否在限值范围内

#### 示例

```xml
<!-- 直接提供数值 -->
<AddNumericMeasure
    label="电压测量"
    lower_limit="3.0"
    upper_limit="5.0"
    unit="V"
    data_value="4.5"
    data_type="MEASURE_DATA_TYPE_FLOAT"
/>

<!-- 从黑板变量获取数值 -->
<AddNumericMeasure
    label="电压测量"
    lower_limit="3.0"
    upper_limit="5.0"
    unit="V"
    variable_name="voltage_value"
    data_type="MEASURE_DATA_TYPE_FLOAT"
/>

<!-- 十六进制数据类型 -->
<AddNumericMeasure
    label="寄存器值"
    lower_limit="0"
    upper_limit="255"
    unit="hex"
    variable_name="register_value"
    data_type="MEASURE_DATA_TYPE_HEX"
/>

<!-- 整数数据类型 -->
<AddNumericMeasure
    label="计数值"
    lower_limit="0"
    upper_limit="100"
    unit="count"
    variable_name="count_value"
    data_type="MEASURE_DATA_TYPE_INT"
/>
```

### AddStringMeasureNode

该节点用于添加字符串类型的测量结果。

#### 输入端口

- `label`: 测量项的标签
- `expected_value`: 期望的字符串值
- `actual_value`: 实际测量的字符串值
- `variable_name`: 要从黑板获取的变量名称，如果提供则优先使用

#### 输出端口

- `is_match`: 实际值是否与期望值相同

#### 示例

```xml
<!-- 直接提供字符串值 -->
<AddStringMeasure
    label="固件版本"
    expected_value="v1.2.3"
    actual_value="v1.2.3"
/>

<!-- 从黑板变量获取字符串值 -->
<AddStringMeasure
    label="固件版本"
    expected_value="v1.2.3"
    variable_name="firmware_version"
/>
```

## 使用变量值的工作流程

1. 首先，将数据存储到黑板中：

```xml
<SetBlackboard output_key="voltage_value" value="4.5" />
<SetBlackboard output_key="firmware_version" value="v1.2.3" />
```

2. 然后，使用AddNumericMeasure或AddStringMeasure节点，通过variable_name参数引用这些变量：

```xml
<AddNumericMeasure
    label="电压测量"
    lower_limit="3.0"
    upper_limit="5.0"
    unit="V"
    variable_name="voltage_value"
    data_type="MEASURE_DATA_TYPE_FLOAT"
/>

<AddStringMeasure
    label="固件版本"
    expected_value="v1.2.3"
    variable_name="firmware_version"
/>
```

3. 节点会自动从黑板获取变量值，并将其传递给WidgetLogSetting进行处理。

## 注意事项

- 如果同时提供了`variable_name`和`data_value`/`actual_value`，节点会优先使用`variable_name`从黑板获取值。
- 如果`variable_name`指定的变量不存在于黑板中，节点将返回失败状态。
- 确保在使用这些节点之前，已经正确设置了黑板中的`current_lidar_index`和`g_widget_log_setting`。
