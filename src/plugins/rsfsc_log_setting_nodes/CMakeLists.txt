﻿cmake_minimum_required(VERSION 3.16)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置插件名称
set(PLUGIN_NAME rsfsc_log_setting_nodes)

# 查找Qt包
find_package(
  Qt5
  COMPONENTS Core Widgets
  REQUIRED)

# 设置源文件
set(PLUGIN_SOURCES log_setting_nodes.cpp log_setting_plugin.cpp)

# 设置头文件
set(PLUGIN_HEADERS log_setting_nodes.h log_setting_plugin.h)

# 创建共享库
add_library(${PLUGIN_NAME} SHARED ${PLUGIN_SOURCES} ${PLUGIN_HEADERS})

# 设置包含目录
target_include_directories(${PLUGIN_NAME} PUBLIC ${CMAKE_SOURCE_DIR}/include ${CMAKE_SOURCE_DIR}
                                                 $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>)

# 链接库
target_link_libraries(${PLUGIN_NAME} PRIVATE Qt5::Core Qt5::Widgets common::common)

# 设置输出目录
set_target_properties(${PLUGIN_NAME} PROPERTIES LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/plugins")

# 安装规则
install(TARGETS ${PLUGIN_NAME} LIBRARY DESTINATION plugins)
