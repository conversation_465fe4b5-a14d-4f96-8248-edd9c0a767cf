﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef LOG_SETTING_NODES_H
#define LOG_SETTING_NODES_H

#include <behaviortree_cpp/behavior_tree.h>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

/**
 * @brief 过站检测
 *
 * 调用WidgetLogSetting::checkAllState来验证过站
 */
class CheckLidarStateNode : public BT::SyncActionNode
{
public:
  CheckLidarStateNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief 获取Lidar数据节点
 *
 * 从与当前lidar_index对应的WidgetLidarInfo实例的UI控件中获取用户输入或配置的数据
 */
class GetLidarDataNode : public BT::SyncActionNode
{
public:
  GetLidarDataNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief 添加数值测量节点
 *
 * 将一个带有上下限的数值测量结果记录到WidgetLogSetting
 */
class AddNumericMeasureNode : public BT::SyncActionNode
{
public:
  AddNumericMeasureNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief 添加字符串测量节点
 *
 * 将一个字符串类型的测量结果记录到WidgetLogSetting
 */
class AddStringMeasureNode : public BT::SyncActionNode
{
public:
  AddStringMeasureNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief 设置测试状态节点
 *
 * 设置当前Lidar的最终测试状态（通过、失败、未准备好等）
 */
class SetTestStatusNode : public BT::SyncActionNode
{
public:
  SetTestStatusNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief Finish上传
 *
 * 标志着当前Lidar的所有处理流程结束，触发日志文件的最终写入和可能的资源释放
 */
class FinishLidarProcessNode : public BT::SyncActionNode
{
public:
  FinishLidarProcessNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

/**
 * @brief 设置Lidar配置节点
 *
 * 在流程中动态地设置WidgetLidarInfo的某些配置项
 */
class SetLidarConfigNode : public BT::SyncActionNode
{
public:
  SetLidarConfigNode(const std::string& _name, const BT::NodeConfig& _config);

  static BT::PortsList providedPorts();

  BT::NodeStatus tick() override;
};

}  // namespace robosense::lidar

#endif  // LOG_SETTING_NODES_H
