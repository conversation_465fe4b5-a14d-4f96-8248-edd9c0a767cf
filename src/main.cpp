﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "common/common_types.h"
#include "config.h"
#include "include/widget_log_setting.h"
#include "ui/app_event.h"
#include <QApplication>

#include <QPalette>
#include <QStyleFactory>

namespace
{
void applyQtNodesDarkTheme()
{
  QPalette dark_palette;

  // QtNodes 主要配色方案
  QColor background_color(42, 42, 42);
  QColor base_color(50, 50, 50);
  QColor text_color(220, 220, 220);
  QColor button_color(60, 60, 60);
  QColor highlight_color(42, 130, 218);

  dark_palette.setColor(QPalette::Window, background_color);
  dark_palette.setColor(QPalette::WindowText, text_color);
  dark_palette.setColor(QPalette::Base, button_color);
  dark_palette.setColor(QPalette::AlternateBase, QColor(66, 66, 66));
  dark_palette.setColor(QPalette::ToolTipBase, Qt::white);
  dark_palette.setColor(QPalette::ToolTipText, text_color);
  dark_palette.setColor(QPalette::Text, text_color);
  dark_palette.setColor(QPalette::Button, button_color);
  dark_palette.setColor(QPalette::ButtonText, text_color);
  dark_palette.setColor(QPalette::BrightText, Qt::red);
  dark_palette.setColor(QPalette::Link, highlight_color);

  dark_palette.setColor(QPalette::Highlight, highlight_color);
  dark_palette.setColor(QPalette::HighlightedText, Qt::black);

  // 应用到整个 Qt 程序
  QApplication::setPalette(dark_palette);
  QApplication::setStyle(QStyleFactory::create("Fusion"));
}

int launchWindow(int _argc, char** _argv)
{
  int exit_code = 0;
  auto init_app = [&exit_code, &_argc, &_argv]() {
    //init main window
    QApplication app(_argc, _argv);
    // applyQtNodesDarkTheme();
    robosense::lidar::G_APP->getMainWin()->show();
    exit_code = QApplication::exec();
    robosense::lidar::G_APP->destroyMainWin();
  };

  init_app();

  //check if restart
  while (robosense::lidar::MainWindow::restartCode() == exit_code)
  {
    init_app();
  }

  return exit_code;
}
}  // namespace

int main(int _argc, char* _argv[])
{
  robosense::lidar::rsfsc_lib::WidgetLogSetting::init(
    _argc, _argv, std::string(""), std::string { PROJECT_NAME }, std::string { PROJECT_UNIQUE_CODE },
    FIXTURE_TEST_VERSION_MAJOR, FIXTURE_TEST_VERSION_MINOR, FIXTURE_TEST_VERSION_PATCH, FIXTURE_TEST_VERSION_TWEAK,
    IS_DEBUG_MODE, robosense::lidar::rsfsc_lib::CableManageStatus::DISABLE);

  Q_INIT_RESOURCE(resource);  // https://doc.qt.io/qt-5/resources.html

  qRegisterMetaType<std::string>("std::string");
  qRegisterMetaType<robosense::lidar::NodeInfos>("robosense::lidar::NodeInfos");
  // qRegisterMetaType<std::shared_ptr<BT::TreeNode>>("std::shared_ptr<BT::TreeNode>");
  // qRegisterMetaType<BT::NodeStatus>("BT::NodeStatus");

  return launchWindow(_argc, _argv);
}