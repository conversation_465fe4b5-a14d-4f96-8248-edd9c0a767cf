﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef PROPERTY_VIEW_H
#define PROPERTY_VIEW_H

#include "behaviortree_cpp/contrib/json.hpp"
#include "property_model.h"
#include <memory>
#include <string>
#include <unordered_map>

class QTreeWidgetItem;
class QTreeWidget;
class QPushButton;

namespace robosense::lidar
{

// 属性编辑器视图
class PropertyView : public QWidget
{
  Q_OBJECT
public:
  using Ptr = std::unique_ptr<PropertyView>;
  explicit PropertyView(QWidget* _parent = nullptr, bool _hide_single_group = false, const QString& _name = "");
  ~PropertyView() override;

  PropertyView(const PropertyView&) = delete;
  PropertyView& operator=(const PropertyView&) = delete;
  PropertyView(PropertyView&&)                 = delete;
  PropertyView& operator=(PropertyView&&) = delete;

  void registerInt(const std::string& _group,
                   const std::string& _key,
                   int _default_value = 0,
                   int _min           = std::numeric_limits<int>::min(),
                   int _max           = std::numeric_limits<int>::max(),
                   int _step          = 1);
  void registerDouble(const std::string& _group,
                      const std::string& _key,
                      double _default_value = 0.0,
                      double _min           = -std::numeric_limits<double>::max(),
                      double _max           = std::numeric_limits<double>::max(),
                      double _step          = 0.1,
                      int _decimals         = 2);
  void registerString(const std::string& _group, const std::string& _key, const std::string& _default_value = "");
  void registerString(const std::string& _group,
                      const std::string& _key,
                      const std::string& _default_value,
                      int _height_factor);
  void registerBool(const std::string& _group, const std::string& _key, bool _default_value = false);
  void registerEnum(const std::string& _group,
                    const std::string& _key,
                    const QStringList& _options,
                    int _default_index = 0);
  void registerFilePath(const std::string& _group,
                        const std::string& _key,
                        const std::string& _default_value = "",
                        const std::string& _filter        = "所有文件 (*)");
  void registerDirectoryPath(const std::string& _group,
                             const std::string& _key,
                             const std::string& _default_value = "");
  bool unregisterProperty(const std::string& _group, const std::string& _key);
  bool unregisterGroup(const std::string& _group);

  template <typename T>
  T getValue(const std::string& _group, const std::string& _key) const
  {
    return model_->getValue<T>(_group, _key);
  }
  template <typename T>
  void setValue(const std::string& _group, const std::string& _key, const T& _value)
  {
    model_->setValue<T>(_group, _key, _value);
  }
  void refreshUi();
  bool saveToJson(const QString& _file_path) const;
  bool loadFromJson(const QString& _file_path);
  bool saveToJson(std::string& _json) const;
  bool loadFromJson(const std::string& _json);

  // 使用nlohmann::ordered_json的方法
  bool saveToOrderedJson(std::string& _json) const;
  bool loadFromOrderedJson(const std::string& _json);

  // 加载JSON，但排除指定的组
  bool loadFromJsonExcludeGroups(const std::string& _json, const std::vector<std::string>& _exclude_groups);

  struct BtnHidden
  {
    bool add_btn_hidden { false };
    bool delete_btn_hidden { false };
    bool save_btn_hidden { false };
    bool load_btn_hidden { false };
    bool apply_btn_hidden { false };
  };
  void setButtonHidden(const BtnHidden _hid);

  void setModel(const PropertyModel::Ptr& _model);
  void setModel(const PropertyModel::Ptr&& _model);

  PropertyModel::Ptr model() const { return model_; }

  // 设置只读组列表
  void setReadOnlyGroups(const QStringList& _groups);

  // 添加只读组
  void addReadOnlyGroup(const QString& _group);

  // 移除只读组
  void removeReadOnlyGroup(const QString& _group);

  // 检查组是否只读
  bool isGroupReadOnly(const QString& _group) const;

  // 保存/加载用户实际值
  void saveUserValues();
  void loadUserValues();

private Q_SLOTS:
  void slotAddPropertyClicked();
  void slotDeletePropertyClicked();
  void slotSaveClicked();
  void slotLoadClicked();
  void slotApplyClicked();
  void slotValueChanged(const std::string& _group, const std::string& _key, const QVariant& _value);
  void slotModelChanged();

private:
  void setupUi();
  QTreeWidgetItem* findOrCreateGroupItem(const std::string& _group);
  void setupButtons();
  template <typename RegisterFunc, typename... Args>
  void registerAndCreateUi(const std::string& _group, const std::string& _key, RegisterFunc& _func, Args&&... _args);
  void disconnectModel();
  void connectModel();

  PropertyModel::Ptr model_ { nullptr };
  std::unordered_map<std::string, QTreeWidgetItem*> group_items_;
  QPushButton* add_button_ { nullptr };
  QPushButton* delete_button_ { nullptr };
  QPushButton* save_button_ { nullptr };
  QPushButton* load_button_ { nullptr };
  QPushButton* apply_button_ { nullptr };
  QTreeWidget* widget_tree_ { nullptr };
  bool hide_single_group_ { false };
  QStringList read_only_groups_;  // 不允许添加/删除的组列表
};

}  // namespace robosense::lidar

#endif  // PROPERTY_VIEW_H
