﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "async_plugin_loader.h"
#include "plugin_manager.h"
#include "rsfsc_log/rsfsc_log_macro.h"

namespace robosense::lidar
{

AsyncPluginLoader::AsyncPluginLoader()
{
  // 将对象移动到工作线程
  this->moveToThread(&worker_thread_);

  // 连接信号和槽
  connect(this, &AsyncPluginLoader::signalStartLoading, this, &AsyncPluginLoader::loadPlugins, Qt::QueuedConnection);

  // 启动工作线程
  worker_thread_.start();
}

AsyncPluginLoader::~AsyncPluginLoader() noexcept
{
  // 请求线程退出并等待完成
  worker_thread_.quit();
  worker_thread_.wait();
}

void AsyncPluginLoader::startLoading(const QString& _plugin_dir)
{
  if (is_loading_.load())
  {
    LOG_WARN("插件加载器已经在运行中，请等待当前加载完成");
    return;
  }

  is_loading_.store(true);
  cancel_requested_.store(false);

  // 发送信号到工作线程开始加载
  Q_EMIT signalStartLoading(_plugin_dir);
}

bool AsyncPluginLoader::isLoading() const { return is_loading_.load(); }

void AsyncPluginLoader::cancelLoading()
{
  if (is_loading_.load())
  {
    cancel_requested_.store(true);
  }
}

void AsyncPluginLoader::loadPlugins(const QString& _plugin_dir)
{
  QDir dir(_plugin_dir);
  if (!dir.exists())
  {
    is_loading_.store(false);
    Q_EMIT signalLoadError(QString("插件目录不存在: %1").arg(_plugin_dir));
    return;
  }

  // 获取所有插件文件
  QStringList filters;
#ifdef Q_OS_WIN
  filters << "*.dll";
#else
  filters << "*.so"
          << "*.dylib";
#endif

  dir.setNameFilters(filters);
  QStringList plugin_files = dir.entryList(QDir::Files);

  int total_count  = plugin_files.size();
  int loaded_count = 0;

  LOG_INFO("找到 {} 个插件文件", total_count);

  // 逐个加载插件
  for (int i = 0; i < plugin_files.size(); ++i)
  {
    // 检查是否请求取消
    if (cancel_requested_.load())
    {
      LOG_INFO("插件加载已取消");
      break;
    }

    const QString& file_name = plugin_files[i];
    QString file_path        = dir.absoluteFilePath(file_name);

    // 发送进度信号
    Q_EMIT signalLoadProgress(i + 1, total_count, file_name);

    // 使用PluginManager加载单个插件文件
    if (PluginManager::getInstance()->loadPluginFile(file_path) > 0)
    {
      loaded_count++;
    }
  }

  // 加载完成
  is_loading_.store(false);
  Q_EMIT signalLoadFinished(loaded_count, total_count);
}

}  // namespace robosense::lidar
