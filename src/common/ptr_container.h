﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef PTR_CONTAINER_H
#define PTR_CONTAINER_H

#include <memory>
#include <tuple>

namespace robosense::lidar::comm
{

/**
 * @brief 指针容器
 * 
 * @tparam Args 对象指针
 */
template <typename... Args>
struct PtrContainer
{
  PtrContainer(PtrContainer&&) noexcept = default;
  PtrContainer& operator=(PtrContainer&&) noexcept = default;
  explicit PtrContainer(Args*... _args) : all_impl_ptr_(std::unique_ptr<Args>(_args)...) {}
  ~PtrContainer() = default;
  template <typename T>
  T& get() const
  {
    return *std::get<std::unique_ptr<T>>(all_impl_ptr_).get();
  }

public:
  PtrContainer(const PtrContainer&) = delete;
  PtrContainer& operator=(const PtrContainer&) = delete;

private:
  std::tuple<std::unique_ptr<Args>...> all_impl_ptr_;
};

}  // namespace robosense::lidar::comm

#endif  // PTR_CONTAINER_H
