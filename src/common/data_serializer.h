﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef BT_VALIDATOR_H
#define BT_VALIDATOR_H

#include "rs_expected.h"
#include "singleton.h"
#include <string>

namespace QtNodes
{
class DataFlowGraphModel;
class DataFlowGraphicsScene;
using NodeId = unsigned int;
}  // namespace QtNodes

class QString;

namespace robosense::lidar
{

class PropertyView;

/**
 * @brief 工程文件序列化处理器
 */
class DataSerializer : public Singleton<DataSerializer>
{
public:
  ~DataSerializer() override;
  DataSerializer(const DataSerializer&) = delete;
  DataSerializer& operator=(const DataSerializer&) = delete;
  DataSerializer(DataSerializer&&)                 = delete;
  DataSerializer& operator=(DataSerializer&&) = delete;
  friend class Singleton<DataSerializer>;

  void setDataFlow(QtNodes::DataFlowGraphicsScene* _scene_impl, QtNodes::DataFlowGraphModel* _graph_model);
  void setProperty(PropertyView* _property_view);

  /**
   * @brief 验证并生成XML (DFS)
   *
   * @param _path 文件路径
   * @return Expected<std::string, std::string> 成功返回XML内容，失败返回error信息
   */
  Expected<std::string, std::string> validateAndGenerateXml(const QString& _path);

  /**
   * @brief 保存统一JSON文件，包含BT_XML + 节点布局信息json + 参数配置json
   * @param _file_path 文件路径
   * @return 是否成功保存
   */
  bool saveToUnifiedFile(const QString& _file_path);

  /**
   * @brief 从统一JSON文件加载行为树，包含XML和节点布局信息
   * @param _file_path 文件路径
   * @return 是否成功加载
   */
  bool loadFromUnifiedFile(const QString& _file_path);

  Expected<std::string, std::string> saveProperty();

private:
  explicit DataSerializer();
  class Impl;
  Impl* pimpl_;
};

}  // namespace robosense::lidar

#endif  // BT_VALIDATOR_H
