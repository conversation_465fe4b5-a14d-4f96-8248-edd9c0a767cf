﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef ASYNC_PLUGIN_LOADER_H
#define ASYNC_PLUGIN_LOADER_H

#include "singleton.h"
#include <QDir>
#include <QMutex>
#include <QObject>
#include <QStringList>
#include <QThread>
#include <QWaitCondition>
#include <atomic>
#include <memory>
#include <string>
#include <vector>

namespace robosense::lidar
{

/**
 * @brief 异步插件加载器，在单独的Qt线程中加载插件
 * 
 * 该类用于在后台线程中异步加载插件，避免在主线程中加载插件导致UI卡顿
 */
class AsyncPluginLoader : public QObject, public Singleton<AsyncPluginLoader>
{
  Q_OBJECT

public:
  AsyncPluginLoader(const AsyncPluginLoader&) = delete;
  AsyncPluginLoader& operator=(const AsyncPluginLoader&) = delete;
  AsyncPluginLoader(AsyncPluginLoader&&)                 = delete;
  AsyncPluginLoader& operator=(AsyncPluginLoader&&) = delete;
  friend class Singleton<AsyncPluginLoader>;
  ~AsyncPluginLoader() noexcept override;

  /**
   * @brief 开始异步加载插件
   * @param _plugin_dir 插件目录
   */
  void startLoading(const QString& _plugin_dir);

  /**
   * @brief 检查是否正在加载插件
   * @return 是否正在加载插件
   */
  [[nodiscard]] bool isLoading() const;

  /**
   * @brief 取消加载
   */
  void cancelLoading();

Q_SIGNALS:
  /**
   * @brief 加载进度信号
   * @param _current 当前加载的插件索引
   * @param _total 总插件数量
   * @param _plugin_name 当前加载的插件名称
   */
  void signalLoadProgress(int _current, int _total, const QString& _plugin_name);

  /**
   * @brief 加载完成信号
   * @param _loaded_count 成功加载的插件数量
   * @param _total_count 总插件数量
   */
  void signalLoadFinished(int _loaded_count, int _total_count);

  /**
   * @brief 加载错误信号
   * @param _error_message 错误信息
   */
  void signalLoadError(const QString& _error_message);

  /**
   * @brief 内部使用的开始加载信号
   * @param _plugin_dir 插件目录
   */
  void signalStartLoading(const QString& _plugin_dir);

private:
  AsyncPluginLoader();

  /**
   * @brief 执行插件加载
   * @param _plugin_dir 插件目录
   */
  void loadPlugins(const QString& _plugin_dir);

private:
  QThread worker_thread_;
  std::atomic<bool> is_loading_ { false };
  std::atomic<bool> cancel_requested_ { false };
};

}  // namespace robosense::lidar

#endif  // ASYNC_PLUGIN_LOADER_H
