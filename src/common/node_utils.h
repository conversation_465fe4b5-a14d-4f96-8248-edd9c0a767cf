﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef NODE_UTILS_H
#define NODE_UTILS_H

#include "rs_expected.h"
#include <QDomDocument>
#include <QtNodes/DataFlowGraphModel>
#include <QtNodes/DataFlowGraphicsScene>

#include "node_models/abs_behavior_tree.h"

namespace robosense::lidar
{

class Utils
{
public:
  static Expected<QtNodes::NodeId, std::string> findRoot(QtNodes::DataFlowGraphModel* _graph_model);
  static std::vector<QtNodes::NodeId> getChildren(QtNodes::DataFlowGraphModel* _graph_model,
                                                  const QtNodes::NodeId& _parent_node_id,
                                                  bool _ordered);
  static AbsBehaviorTree buildTreeFromScene(QtNodes::DataFlowGraphModel* _graph_model, QtNodes::NodeId _root_id);
  static AbsBehaviorTree buildTreeFromXML(const QDomElement& _bt_root, const NodeModels& _models);

  static void nodeReorder(QtNodes::DataFlowGraphModel* _graph_model, AbsBehaviorTree& _tree);

  // std::pair<QtNodes::NodeStyle, QtNodes::ConnectionStyle> getStyleFromStatus(BT::NodeStatus status,
  //                                                                            BT::NodeStatus prev_status);

  // QtNodes::Node* GetParentNode(QtNodes::Node* node);

  static std::set<QString> getModelsToRemove(QWidget* _parent, NodeModels& _prev_models, const NodeModels& _new_models);

  static void showWidget(QWidget* _widget);
  static void closeWidget(QWidget* _widget, bool _is_delete);
};
}  // namespace robosense::lidar

#endif  // NODE_UTILS_H
