﻿cmake_minimum_required(VERSION 3.10)

# Add side_panel subdirectory
add_subdirectory(side_panel)

set(FLOW_EDITOR flow_editor)

# Generate dynamic library
add_library(${FLOW_EDITOR} STATIC)

# Link Qt5 and common library
target_link_libraries(${FLOW_EDITOR} PRIVATE common::common)

target_sources(${FLOW_EDITOR} PRIVATE test_flow_editor.h qt_logger.h test_flow_editor.cpp $<TARGET_OBJECTS:side_panel>)

# Set include directories
target_include_directories(${FLOW_EDITOR} PUBLIC ${CMAKE_SOURCE_DIR})

# Set output directory
set_target_properties(
  ${FLOW_EDITOR}
  PROPERTIES POSITION_INDEPENDENT_CODE ON
             CXX_EXTENSIONS NO
             CMAKE_AUTOMOC ON
             CMAKE_AUTOUIC ON
             CMAKE_AUTORCC ON)
