﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SIDE_PANEL_NODE_VIEW_H
#define SIDE_PANEL_NODE_VIEW_H

#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMouseEvent>
#include <QTreeView>

namespace robosense::lidar
{

class SidePanelNodeModel;

/**
 * @brief 节点面板视图类，显示节点面板模型
 */
class SidePanelNodeView : public QTreeView
{
  Q_OBJECT

public:
  /**
     * @brief 构造函数
     * @param _parent 父窗口
     */
  explicit SidePanelNodeView(QWidget* _parent = nullptr);

  /**
     * @brief 析构函数
     */
  ~SidePanelNodeView() override = default;

  SidePanelNodeView(const SidePanelNodeView&) = delete;
  SidePanelNodeView& operator=(const SidePanelNodeView&) = delete;
  SidePanelNodeView(SidePanelNodeView&&)                 = delete;
  SidePanelNodeView& operator=(SidePanelNodeView&&) = delete;

  /**
     * @brief 设置节点面板模型
     * @param _model 节点面板模型
     */
  void setNodePaletteModel(SidePanelNodeModel* _model);

  /**
     * @brief 获取节点面板模型
     * @return 节点面板模型
     */
  [[nodiscard]] SidePanelNodeModel* nodePaletteModel() const;

protected:
  /**
     * @brief 鼠标按下事件处理
     * @param _event 鼠标事件
     */
  void mousePressEvent(QMouseEvent* _event) override;

  /**
     * @brief 鼠标移动事件处理
     * @param _event 鼠标事件
     */
  void mouseMoveEvent(QMouseEvent* _event) override;

private:
  QPoint drag_start_position_;  // 拖拽开始位置
};

}  // namespace robosense::lidar

#endif  // SIDE_PANEL_NODE_VIEW_H
