﻿cmake_minimum_required(VERSION 3.10)

set(SIDE_PANEL side_panel)

# Generate object library
add_library(${SIDE_PANEL} OBJECT)

# Link Qt5 and common library
target_link_libraries(${SIDE_PANEL} PRIVATE common::common)

target_sources(${SIDE_PANEL} PRIVATE side_panel_node_model.h side_panel_node_model.cpp side_panel_node_view.h
                                     side_panel_node_view.cpp side_panel_node_widget.h side_panel_node_widget.cpp)

# Set include directories
target_include_directories(
  ${SIDE_PANEL} PUBLIC $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}> $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/src/flow_editor>
                       $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/src> $<INSTALL_INTERFACE:include>)

# Set output directory
set_target_properties(
  ${SIDE_PANEL}
  PROPERTIES POSITION_INDEPENDENT_CODE ON
             CXX_EXTENSIONS NO
             CMAKE_AUTOMOC ON
             CMAKE_AUTOUIC ON
             CMAKE_AUTORCC ON)

# For object libraries, we need to set these properties
set_property(TARGET ${SIDE_PANEL} PROPERTY POSITION_INDEPENDENT_CODE ON)
