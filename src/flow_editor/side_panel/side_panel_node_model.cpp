﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "side_panel_node_model.h"
#include <QColor>
#include <QDataStream>
#include <QDebug>
#include <QFont>
#include <QMimeData>
#include <QtGlobal>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

namespace
{
// 辅助函数：计算标题项数量
int countTitles(const std::vector<NodePaletteItem>& _items)
{
  return std::count_if(_items.begin(), _items.end(), [](const NodePaletteItem& _item) { return _item.is_title; });
}

// 辅助函数：检查索引和ID的有效性
bool isValidIndex(quint64 _id, const std::vector<NodePaletteItem>& _items)
{
  return _id < static_cast<quint64>(_items.size());
}

// 辅助函数：查找最后一个标题项的索引
int findLastTitleIndex(const std::vector<NodePaletteItem>& _items)
{
  for (int i = _items.size() - 1; i >= 0; --i)
  {
    if (_items[i].is_title)
    {
      return i;
    }
  }
  return -1;
}

// 辅助函数：计算标题下的子项数量
int countChildrenUnderTitle(const std::vector<NodePaletteItem>& _items, int _title_index)
{
  int count = 0;
  for (int i = _title_index + 1; i < static_cast<int>(_items.size()) && !_items[i].is_title; ++i)
  {
    count++;
  }
  return count;
}
}  // namespace

// NodePaletteItem 普通节点构造函数实现
NodePaletteItem::NodePaletteItem(const std::string& _display_name,
                                 const std::string& _registered_model_name,
                                 const std::string& _category,
                                 const QIcon& _icon) :
  display_name(_display_name),
  registered_model_name(_registered_model_name),
  category(_category),
  is_title(false),
  icon(_icon)
{}

// NodePaletteItem 标题项构造函数实现
NodePaletteItem::NodePaletteItem(const QString& _title) :
  display_name(_title.toStdString()), registered_model_name(""), category(""), is_title(true)
{}

SidePanelNodeModel::SidePanelNodeModel(QObject* _parent) :
  QAbstractItemModel(_parent), mime_type_("robosense/test_flow_node")
{}

int SidePanelNodeModel::rowCount(const QModelIndex& _parent) const
{
  if (!_parent.isValid())
  {
    return countTitles(items_);
  }

  auto internal_id = _parent.internalId();
  if (!isValidIndex(internal_id, items_) || !items_[internal_id].is_title)
  {
    return 0;
  }

  return countChildrenUnderTitle(items_, static_cast<int>(internal_id));
}

int SidePanelNodeModel::columnCount(const QModelIndex& /*_idx*/) const { return 1; }

QModelIndex SidePanelNodeModel::index(int _row, int _column, const QModelIndex& _parent) const
{
  if (!hasIndex(_row, _column, _parent))
  {
    LOG_ERROR("无效的索引: row={}, column={}", _row, _column);
    return {};
  }

  if (_parent.isValid())
  {
    auto parent_id = _parent.internalId();
    if (!isValidIndex(parent_id, items_) || !items_[parent_id].is_title)
    {
      LOG_ERROR("父索引无效或不是标题项: parent_id={}", parent_id);
      return {};
    }

    int child_id = static_cast<int>(parent_id) + 1;
    for (int i = 0; i < _row; ++i)
    {
      if (child_id >= static_cast<int>(items_.size()) || items_[child_id].is_title)
      {
        LOG_ERROR("子项索引无效或是标题项: child_id={}, row={}", child_id, _row);
        return {};
      }
      child_id++;
    }

    return createIndex(_row, _column, static_cast<quint64>(child_id));
  }

  // 根级别索引（标题项）
  int title_id      = -1;
  int current_title = 0;
  for (int i = 0; i < static_cast<int>(items_.size()); ++i)
  {
    if (items_[i].is_title)
    {
      if (current_title == _row)
      {
        title_id = i;
        break;
      }
      current_title++;
    }
  }

  return title_id != -1 ? createIndex(_row, _column, static_cast<quint64>(title_id)) : QModelIndex();
}

QModelIndex SidePanelNodeModel::parent(const QModelIndex& _index) const
{
  if (!_index.isValid())
  {
    LOG_ERROR("获取父索引失败: 索引无效");
    return {};
  }

  auto item_id = _index.internalId();
  if (!isValidIndex(item_id, items_))
  {
    LOG_ERROR("获取父索引失败: 项目ID超出范围: item_id={}, items_size={}", item_id, items_.size());
    return {};
  }

  if (items_[item_id].is_title)
  {
    return {};
  }

  // 向前查找父项（最近的标题项）
  int parent_id  = -1;
  int parent_row = 0;
  for (auto idx = item_id - 1; idx < item_id; --idx)
  {
    if (items_[idx].is_title)
    {
      parent_id = static_cast<int>(idx);
      break;
    }
  }

  if (parent_id == -1)
  {
    LOG_ERROR("获取父索引失败: 未找到父标题项");
    return {};
  }

  // 计算父项行号
  for (int i = 0; i < parent_id; ++i)
  {
    if (items_[i].is_title)
    {
      parent_row++;
    }
  }

  return createIndex(parent_row, 0, static_cast<quint64>(parent_id));
}

QVariant SidePanelNodeModel::data(const QModelIndex& _index, int _role) const
{
  if (!_index.isValid() || !isValidIndex(_index.internalId(), items_))
  {
    LOG_ERROR("获取数据失败: 索引无效或ID无效: internalId={}", _index.isValid() ? _index.internalId() : 0);
    return {};
  }

  const NodePaletteItem& item = items_[_index.internalId()];
  switch (_role)
  {
  case DISPLAY_NAME_ROLE: return QString::fromStdString(item.display_name);
  case REGISTERED_MODEL_NAME_ROLE: return QString::fromStdString(item.registered_model_name);
  case CATEGORY_ROLE: return QString::fromStdString(item.category);
  case IS_TITLE_ROLE: return item.is_title;
  case ICON_ROLE: return item.icon;
  case Qt::FontRole:
    if (item.is_title)
    {
      // 为标题项设置加粗字体
      QFont font;
      font.setBold(true);
      return font;
    }
    return {};
  case Qt::ForegroundRole:
    if (item.is_title)
    {
      // 为标题项设置黑色文本
      return QColor(Qt::black);
    }
    return {};
  case Qt::ToolTipRole:
    return item.is_title
             ? QString::fromStdString(item.display_name)
             : QString::fromStdString(fmt::format("类型: {}\n类别: {}", item.registered_model_name, item.category));
  default: return {};
  }
}

Qt::ItemFlags SidePanelNodeModel::flags(const QModelIndex& _index) const
{
  if (!_index.isValid() || !isValidIndex(_index.internalId(), items_))
  {
    LOG_ERROR("获取项目标志失败: 索引无效或ID无效: internalId={}", _index.isValid() ? _index.internalId() : 0);
    return Qt::NoItemFlags;
  }

  return items_[_index.internalId()].is_title ? (Qt::ItemIsEnabled | Qt::ItemIsSelectable)
                                              : (Qt::ItemIsEnabled | Qt::ItemIsSelectable | Qt::ItemIsDragEnabled);
}

QStringList SidePanelNodeModel::mimeTypes() const { return QStringList() << mime_type_; }

QMimeData* SidePanelNodeModel::mimeData(const QModelIndexList& _indexes) const
{
  if (_indexes.isEmpty() || !_indexes.first().isValid() || !isValidIndex(_indexes.first().internalId(), items_))
  {
    LOG_ERROR("获取MIME数据失败: 索引列表为空或第一个索引无效");
    return nullptr;
  }

  const NodePaletteItem& item = items_[_indexes.first().internalId()];
  if (item.is_title)
  {
    LOG_ERROR("获取MIME数据失败: 选中的是标题项，不支持拖拽");
    return nullptr;
  }

  auto* mime_data = new QMimeData();
  QByteArray encoded_data;
  QDataStream stream(&encoded_data, QIODevice::WriteOnly);
  stream << QString::fromStdString(item.registered_model_name) << QString::fromStdString(item.category);
  mime_data->setData(mime_type_, encoded_data);
  return mime_data;
}

void SidePanelNodeModel::addTitle(const QString& _title)
{
  int title_count = countTitles(items_);
  beginInsertRows(QModelIndex(), title_count, title_count);
  items_.emplace_back(_title);
  endInsertRows();
}

void SidePanelNodeModel::addNodeItem(const std::string& _display_name,
                                     const std::string& _registered_model_name,
                                     const std::string& _category,
                                     const QIcon& _icon)
{
  int last_title_index = findLastTitleIndex(items_);
  if (last_title_index == -1)
  {
    LOG_ERROR("添加节点项失败: 未找到标题项，将添加到根级别");
    beginInsertRows(QModelIndex(), 0, 0);
    items_.emplace_back(_display_name, _registered_model_name, _category, _icon);
    endInsertRows();
    return;
  }

  int title_count = countTitles(items_) - 1;
  int child_count = countChildrenUnderTitle(items_, last_title_index);

  QModelIndex parent_index = createIndex(title_count, 0, static_cast<quint64>(last_title_index));

  beginInsertRows(parent_index, child_count, child_count);
  items_.emplace_back(_display_name, _registered_model_name, _category, _icon);
  endInsertRows();
}

void SidePanelNodeModel::addPluginNodes(const robosense::lidar::NodeInfos& _plugin_nodes)
{
  if (_plugin_nodes.nodes.empty())
  {
    return;
  }

  try
  {
    // 添加插件节点组标题
    std::string plugin_name = _plugin_nodes.plugin_name.empty() ? "未命名插件" : _plugin_nodes.plugin_name;
    addTitle(QString::fromStdString(fmt::format("-- 插件 ({})", plugin_name)));

    // 遍历并添加每个插件节点
    for (const auto& plugin_node : _plugin_nodes.nodes)
    {
      // 检查节点信息是否有效
      if (plugin_node.node_type.empty())
      {
        LOG_WARN("添加插件节点失败: 节点类型为空");
        continue;
      }

      std::string display_name = plugin_node.display_name.empty() ? plugin_node.node_type : plugin_node.display_name;
      std::string category     = plugin_node.category.empty() ? "Plugin" : plugin_node.category;

      addNodeItem(display_name + " (" + plugin_node.node_type + ")", plugin_node.node_type, category);
    }
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("添加插件节点时发生异常: {}", e.what());
  }
  catch (...)
  {
    LOG_ERROR("添加插件节点时发生未知异常");
  }
}

bool SidePanelNodeModel::removeItem(int _index)
{
  if (items_.empty())
  {
    LOG_ERROR("移除项目失败: 项目列表为空");
    return false;
  }

  if (_index < 0 || _index >= static_cast<int>(items_.size()))
  {
    LOG_ERROR("移除项目失败: 索引超出范围: index={}, size={}", _index, items_.size());
    return false;
  }

  // 执行删除操作
  beginRemoveRows(QModelIndex(), _index, _index);
  items_.erase(std::next(items_.begin(), _index));
  endRemoveRows();
  return true;
}

int SidePanelNodeModel::removeItemsByCategory(const std::string& _category)
{
  if (items_.empty())
  {
    return 0;
  }

  if (_category.empty())
  {
    LOG_ERROR("移除项目失败: 类别名称为空");
    return 0;
  }

  int removed_count = 0;

  for (int i = static_cast<int>(items_.size()) - 1; i >= 0; --i)
  {
    if (items_[i].category != _category)
    {
      continue;
    }

    beginRemoveRows(QModelIndex(), i, i);
    items_.erase(items_.begin() + i);
    endRemoveRows();
    removed_count++;
  }

  return removed_count;
}

bool SidePanelNodeModel::removeItemByRegisteredName(const std::string& _registered_model_name)
{
  if (items_.empty())
  {
    LOG_ERROR("移除项目失败: 项目列表为空");
    return false;
  }

  if (_registered_model_name.empty())
  {
    LOG_ERROR("移除项目失败: 注册名称为空");
    return false;
  }

  for (int i = 0; i < static_cast<int>(items_.size()); ++i)
  {
    if (items_[i].registered_model_name != _registered_model_name)
    {
      continue;
    }

    beginRemoveRows(QModelIndex(), i, i);
    items_.erase(items_.begin() + i);
    endRemoveRows();
    return true;
  }

  // 未找到匹配项
  LOG_ERROR("移除项目失败: 未找到注册名称为 {} 的项目", _registered_model_name);
  return false;
}

void SidePanelNodeModel::clear()
{
  if (items_.empty())
  {
    LOG_ERROR("清空项目失败: 项目列表已为空");
    return;
  }

  beginRemoveRows(QModelIndex(), 0, static_cast<int>(items_.size()) - 1);
  items_.clear();
  endRemoveRows();
}

void SidePanelNodeModel::setMimeType(const QString& _mime_type) { mime_type_ = _mime_type; }

}  // namespace robosense::lidar
