﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "side_panel_node_view.h"
#include "side_panel_node_model.h"

#include <QApplication>
#include <QDebug>
#include <QDrag>
#include <QMimeData>
#include <QVariant>
#include <rsfsc_log/rsfsc_log_macro.h>

namespace robosense::lidar
{

SidePanelNodeView::SidePanelNodeView(QWidget* _parent) : QTreeView(_parent)
{
  // 设置拖拽模式
  setDragEnabled(true);
  setDragDropMode(QAbstractItemView::DragOnly);

  // 设置选择模式
  setSelectionMode(QAbstractItemView::SingleSelection);

  // 设置视图属性
  // setAlternatingRowColors(true);

  // 设置树形视图特有属性
  setAnimated(true);              // 启用动画效果
  setExpandsOnDoubleClick(true);  // 双击展开/折叠
  setHeaderHidden(true);          // 隐藏表头
  setIndentation(15);             // 设置缩进
  setRootIsDecorated(true);       // 显示根节点的展开/折叠图标
  setItemsExpandable(true);       // 允许展开/折叠项目

  setStyleSheet(("QTreeView::item {border:1px solid #E8E8E8}"
                 "QTreeView::item::selected {background-color:#4682B4}"));
}

void SidePanelNodeView::setNodePaletteModel(SidePanelNodeModel* _model)
{
  setModel(_model);

  // 自动展开所有项目
  expandAll();
}

SidePanelNodeModel* SidePanelNodeView::nodePaletteModel() const { return qobject_cast<SidePanelNodeModel*>(model()); }

void SidePanelNodeView::mousePressEvent(QMouseEvent* _event)
{
  // 保存拖拽开始位置
  if (_event->button() == Qt::LeftButton)
  {
    drag_start_position_ = _event->pos();
  }

  QTreeView::mousePressEvent(_event);
}

void SidePanelNodeView::mouseMoveEvent(QMouseEvent* _event)
{
  // 检查是否应该开始拖拽
  if (!(_event->buttons() & Qt::LeftButton))
  {
    return;
  }

  // 检查拖拽距离
  if ((_event->pos() - drag_start_position_).manhattanLength() < QApplication::startDragDistance())
  {
    return;
  }

  // 获取当前选中的项目
  QModelIndex index = currentIndex();
  if (!index.isValid())
  {
    LOG_ERROR("拖拽失败: 当前索引无效");
    return;
  }

  // 检查项目是否可拖拽
  if (!(model()->flags(index) & Qt::ItemIsDragEnabled))
  {
    return;
  }

  // 获取MIME数据
  QMimeData* mime_data = model()->mimeData(QModelIndexList() << index);
  if (mime_data == nullptr)
  {
    LOG_ERROR("拖拽失败: 无法获取MIME数据");
    return;
  }

  // 创建拖拽对象
  QDrag* drag = new QDrag(this);
  drag->setMimeData(mime_data);

  // 设置拖拽图标（可选）
  //   QIcon icon = model()->data(index, Qt::DecorationRole).value<QIcon>();
  //   if (!icon.isNull())
  //   {
  //     drag->setPixmap(icon.pixmap(32, 32));
  //     drag->setHotSpot(QPoint(16, 16));
  //   }

  // 执行拖拽操作
  Qt::DropAction result = drag->exec(Qt::CopyAction);

  // 拖拽完成后的处理（如果需要）
  if (result == Qt::IgnoreAction)
  {
    LOG_DEBUG("拖拽被取消或未被接受");
  }
}

}  // namespace robosense::lidar
