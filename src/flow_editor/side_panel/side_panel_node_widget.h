﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SIDE_PANEL_NODE_WIDGET_H
#define SIDE_PANEL_NODE_WIDGET_H

#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>
#include <qobjectdefs.h>

#include "side_panel_node_model.h"

namespace robosense::lidar
{

class SidePanelNodeModel;
class SidePanelNodeView;
class PluginManager;

/**
 * @brief 节点面板组件类，组合模型和视图
 */
class SidePanelWidget : public QWidget
{
  Q_OBJECT
public:
  /**
     * @brief 构造函数
     * @param _parent 父窗口
     */
  explicit SidePanelWidget(QWidget* _parent = nullptr);

  /**
     * @brief 析构函数
     */
  ~SidePanelWidget() override = default;

  SidePanelWidget(const SidePanelWidget&) = delete;
  SidePanelWidget& operator=(const SidePanelWidget&) = delete;
  SidePanelWidget(SidePanelWidget&&)                 = delete;
  SidePanelWidget& operator=(SidePanelWidget&&) = delete;

  /**
     * @brief 初始化基本节点
     */
  void initializeBasicNodes();

  /**
     * @brief 设置MIME类型
     * @param _mime_type MIME类型
     */
  void setMimeType(const QString& _mime_type);

  /**
     * @brief 获取节点面板模型
     * @return 节点面板模型
     */
  [[nodiscard]] SidePanelNodeModel* model() const;

  /**
     * @brief 获取节点面板视图
     * @return 节点面板视图
     */
  [[nodiscard]] SidePanelNodeView* view() const;

public Q_SLOTS:
  /**
     * @brief 添加插件节点
     * @param _plugin_nodes 插件节点信息列表
     */
  void slotAddPluginNodes(const robosense::lidar::NodeInfos& _plugin_nodes);

  /**
     * @brief 添加标题项
     * @param _title 标题
     */
  void addTitle(const QString& _title);

  /**
     * @brief 添加节点项
     * @param _display_name 显示名称
     * @param _registered_model_name 注册模型名称
     * @param _category 类别
     */
  void addNodeItem(const std::string& _display_name,
                   const std::string& _registered_model_name,
                   const std::string& _category);

  /**
     * @brief 移除项目
     * @param _index 索引
     * @return 是否成功移除
     */
  bool removeItem(int _index);

  /**
     * @brief 移除指定类别的所有项目
     * @param _category 类别
     * @return 移除的项目数量
     */
  int removeItemsByCategory(const std::string& _category);

  /**
     * @brief 移除指定注册模型名称的项目
     * @param _registered_model_name 注册模型名称
     * @return 是否成功移除
     */
  bool removeItemByRegisteredName(const std::string& _registered_model_name);

  /**
     * @brief 清空所有项目
     */
  void clear();

  /**
     * @brief 过滤节点
     * @param _filter 过滤文本
     */
  void filterNodes(const QString& _filter);

private Q_SLOTS:
  /**
     * @brief 搜索框文本变化处理
     * @param _text 搜索文本
     */
  void slotSearchTextChanged(const QString& _text);

  /**
     * @brief 清除搜索按钮点击处理
     */
  void slotClearSearchClicked();

private:
  SidePanelNodeModel* model_ { nullptr };  // 节点面板模型
  SidePanelNodeView* view_ { nullptr };    // 节点面板视图
  QLineEdit* search_edit_ { nullptr };     // 搜索框
  QPushButton* clear_button_ { nullptr };  // 清除搜索按钮
  QVBoxLayout* layout_ { nullptr };        // 主布局
};

}  // namespace robosense::lidar

#endif  // SIDE_PANEL_NODE_WIDGET_H
