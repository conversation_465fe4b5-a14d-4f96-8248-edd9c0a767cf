﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SIDE_PANEL_NODE_MODEL_H
#define SIDE_PANEL_NODE_MODEL_H

#include "../../common/common_types.h"
#include <QAbstractItemModel>
#include <QIcon>
#include <QString>
#include <vector>

namespace robosense::lidar
{

// 节点面板项目数据
struct NodePaletteItem
{
  std::string display_name;
  std::string registered_model_name;
  std::string category;
  bool is_title;
  QIcon icon;

  NodePaletteItem(const std::string& _display_name,
                  const std::string& _registered_model_name,
                  const std::string& _category,
                  const QIcon& _icon = QIcon());

  explicit NodePaletteItem(const QString& _title);
};

// 节点面板模型类
class SidePanelNodeModel : public QAbstractItemModel
{
  Q_OBJECT

public:
  enum NodePaletteRoles : std::uint16_t
  {
    DISPLAY_NAME_ROLE          = Qt::DisplayRole,
    REGISTERED_MODEL_NAME_ROLE = Qt::UserRole,
    CATEGORY_ROLE              = Qt::UserRole + 1,
    IS_TITLE_ROLE              = Qt::UserRole + 2,
    ICON_ROLE                  = Qt::DecorationRole
  };

  explicit SidePanelNodeModel(QObject* _parent = nullptr);
  ~SidePanelNodeModel() override = default;

  SidePanelNodeModel(const SidePanelNodeModel&) = delete;
  SidePanelNodeModel& operator=(const SidePanelNodeModel&) = delete;
  SidePanelNodeModel(SidePanelNodeModel&&) noexcept        = delete;
  SidePanelNodeModel& operator=(SidePanelNodeModel&&) noexcept = delete;

  // QAbstractItemModel 接口实现
  [[nodiscard]] int rowCount(const QModelIndex& _parent) const override;
  [[nodiscard]] int columnCount(const QModelIndex& _parent) const override;
  [[nodiscard]] QModelIndex index(int _row, int _column, const QModelIndex& _parent) const override;
  [[nodiscard]] QModelIndex parent(const QModelIndex& _index) const override;
  [[nodiscard]] QVariant data(const QModelIndex& _index, int _role) const override;
  [[nodiscard]] Qt::ItemFlags flags(const QModelIndex& _index) const override;
  [[nodiscard]] QStringList mimeTypes() const override;
  [[nodiscard]] QMimeData* mimeData(const QModelIndexList& _indexes) const override;

  // 节点操作接口
  void addTitle(const QString& _title);
  void addNodeItem(const std::string& _display_name,
                   const std::string& _registered_model_name,
                   const std::string& _category,
                   const QIcon& _icon = QIcon());
  void addPluginNodes(const robosense::lidar::NodeInfos& _plugin_nodes);
  bool removeItem(int _index);
  int removeItemsByCategory(const std::string& _category);
  bool removeItemByRegisteredName(const std::string& _registered_model_name);
  void clear();
  void setMimeType(const QString& _mime_type);

private:
  std::vector<NodePaletteItem> items_;
  QString mime_type_;
};

}  // namespace robosense::lidar

#endif  // SIDE_PANEL_NODE_MODEL_H
