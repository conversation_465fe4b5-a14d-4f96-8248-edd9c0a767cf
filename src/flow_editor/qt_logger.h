﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef BT_QT_LOGGER_H
#define BT_QT_LOGGER_H

#include "behaviortree_cpp/loggers/abstract_logger.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QVariant>
#include <QtCore/QObject>
#include <atomic>
#include <qnamespace.h>
#include <qobjectdefs.h>

// 信号发射器类
class StatusSignalEmitter : public QObject
{
  Q_OBJECT
public:
  explicit StatusSignalEmitter(QObject* _parent = nullptr) : QObject(_parent) {}
Q_SIGNALS:
  void signalStatusChanged(BT::Duration _timestamp,
                           const std::string& _name,
                           BT::NodeStatus _prev_status,
                           BT::NodeStatus _status,
                           const QVariantMap& _output_ports);
};

// 自定义 Logger 类
class QtNodesStatusLogger : public BT::StatusChangeLogger
{
private:
  StatusSignalEmitter* signal_emitter_;
  std::atomic<bool> enabled_;  // 控制 Logger 是否启用

public:
  // 构造函数接收信号发射器实例
  QtNodesStatusLogger(BT::Tree& _tree, StatusSignalEmitter* _emitter) :
    StatusChangeLogger(_tree.rootNode()), signal_emitter_(_emitter), enabled_(true)
  {
    if (_emitter == nullptr)
    {
      throw std::runtime_error("QtNodesStatusLogger requires a valid StatusSignalEmitter");
    }
  }

  void setEmitter(StatusSignalEmitter* _emitter) { signal_emitter_ = _emitter; }

  ~QtNodesStatusLogger() override = default;

  QtNodesStatusLogger(const QtNodesStatusLogger&) = delete;
  QtNodesStatusLogger& operator=(const QtNodesStatusLogger&) = delete;
  QtNodesStatusLogger(QtNodesStatusLogger&&)                 = delete;
  QtNodesStatusLogger& operator=(QtNodesStatusLogger&&) = delete;

  void setEnabled(bool _enable) { enabled_ = _enable; }

  // 重写回调函数
  void callback(BT::Duration _timestamp,
                const BT::TreeNode& _node,
                BT::NodeStatus _prev_status,
                BT::NodeStatus _status) override
  {
    // 检查 Logger 是否启用，以及是否有有效的信号发射器
    if (!enabled_ || (signal_emitter_ == nullptr))
    {
      LOG_DEBUG("Logger is disabled or signal emitter is null, enabled_ = {}", enabled_.load());
      return;
    }
    // 在 BT tick 线程中被调用
    // 发射信号，将数据传递给 UI 线程
    // 注意：信号的发射本身是线程安全的
    LOG_DEBUG("QtNodesStatusLogger::callback: node: {}, prev_status: {}, status: {}", _node.name(),
              BT::toStr(_prev_status), BT::toStr(_status));

    // *** 获取输出端口值 ***
    QVariantMap output_values;
    std::string value_str;
    try
    {
      value_str = _node.config().blackboard->get<std::string>("read_value");
      output_values.insert(QString::fromStdString("read_value"),
                           QVariant::fromValue(QString::fromStdString(value_str)));
      LOG_DEBUG("value_str = {}", value_str);
    }
    catch (const std::exception& e)
    {
      value_str = std::string("<Type: ConvError: ") + e.what() + ">";
    }

    // 发送信号，传递节点名称、上一个状态和当前状态
    Q_EMIT signal_emitter_->signalStatusChanged(_timestamp, _node.name(), _prev_status, _status, output_values);
  }

  void flush() override
  {
    // 对于实时 Logger，flush 通常不需要做什么
  }
};

#endif  // BT_QT_LOGGER_H