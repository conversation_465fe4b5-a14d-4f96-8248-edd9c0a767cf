﻿# 工装平台测试软件

## 1 简介

TODO

## 2 加载工程

```bash
<NAME_EMAIL>:system_codebase/factory_tool/EOS/e1_chip_fractional_bin.git #把代码克隆到本地
git checkout <branch> #切换到对于分支
git submodule init #初始化子模块
git submodule update
```

## 3 Ubuntu

### 3.1 依赖环境

|  依赖  |  版本  |
| :----: | :----: |
| ubuntu | 20.04  |
| CMake  | 3.16.3 |
|   QT   | 5.12.8 |
| BehaviorTree.CPP  | v4.6 |
| QtNodes   | v3 |

### 3.2 编译

```bash
mkdir build && cd build
cmake ../
make
```

### 3.3 安装

目前 cmake 中使用了`make`后进行打包安装的操作，且存放在`release`文件夹中， 进行安装可执行如下操作：

```bash
cd ../release
./install.sh
```

### 3.4 运行

本地运行

```bash
./fixture_test
```

## 4 插件开发

请参考`src/example_plugin.h`和`src/example_plugin.cpp`了解如何开发自定义节点插件。
