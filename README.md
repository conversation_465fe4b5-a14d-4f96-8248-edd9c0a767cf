# 工装平台测试软件

---

## 1. 软件简介

工装平台测试软件是一款基于行为树（BehaviorTree）的可视化测试流程设计和执行平台。软件采用图形化节点编辑方式，支持拖拽式流程设计，适用于雷达等硬件设备的自动化测试。

### 主要特性

- **可视化流程设计**：基于 QtNodes 的图形化节点编辑器
- **行为树架构**：采用 BehaviorTree.CPP v4.6 框架
- **插件化扩展**：支持自定义节点插件开发
- **实时状态监控**：测试过程中实时显示节点执行状态
- **参数化配置**：支持雷达信息、测试参数等配置管理
- **数据序列化**：统一的.rsfsc 文件格式，包含流程、布局和参数信息

---

## 2. 系统要求

### 硬件要求

- CPU：x86_64 架构
- 内存：4GB 以上
- 硬盘：1GB 可用空间

### 软件环境

| 组件             | 版本要求     |
| ---------------- | ------------ |
| 操作系统         | Ubuntu 20.04 |
| CMake            | 3.16.3+      |
| Qt               | 5.12.8       |
| BehaviorTree.CPP | v4.6         |
| QtNodes          | v3           |

---

## 3. 安装与启动

### 3.1 编译安装

```bash
# 克隆代码仓库
git clone <repository_url>
cd fixture_test

# 初始化子模块
git submodule init
git submodule update

# 编译
mkdir build && cd build
cmake ../
make -j4

# 安装（可选）
cd ../release
./install.sh
```

### 3.2 启动软件

```bash
# 本地运行
./fixture_test
```

---

### 4 插件开发指南

#### 创建自定义节点

1. 继承 BT::ActionNode、BT::ConditionNode 或 BT::DecoratorNode
2. 实现 tick()方法定义节点行为
3. 在插件中注册节点类型

#### 示例代码

```cpp
class CustomActionNode : public BT::ActionNode
{
public:
  CustomActionNode(const std::string& name, const BT::NodeConfig& config)
    : BT::ActionNode(name, config) {}

  static BT::PortsList providedPorts()
  {
    return { BT::InputPort<std::string>("input_param") };
  }

  BT::NodeStatus tick() override
  {
    auto param = getInput<std::string>("input_param");
    // 实现自定义逻辑
    return BT::NodeStatus::SUCCESS;
  }
};
```
