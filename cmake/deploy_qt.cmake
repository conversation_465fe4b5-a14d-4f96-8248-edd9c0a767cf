﻿# Instruct CMake to run moc+rcc+uic automatically when needed.
if(POLICY CMP0048)
  cmake_policy(SET CMP0048 NEW)
endif(POLICY CMP0048)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

find_package(
  QT NAMES Qt5
  COMPONENTS Core
  REQUIRED)
find_package(
  Qt${QT_VERSION_MAJOR}
  COMPONENTS Core Gui Widgets
  REQUIRED)

set(QT_DEPEND_LIBRARY $<TARGET_FILE_DIR:Qt${QT_VERSION_MAJOR}::qmake>) # ${QT_DIR}../../../../bin

if(WIN32)
  get_target_property(qmake_executable Qt${QT_VERSION_MAJOR}::qmake IMPORTED_LOCATION)
  get_filename_component(qt_bin_dir "${qmake_executable}" DIRECTORY)
  find_program(WINDEPLOYQT_ENV_SETUP qtenv2.bat HINTS "${qt_bin_dir}")
  if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    find_program(
      WINDEPLOYQT_EXECUTABLE
      NAMES windeployqt.debug.bat
      HINTS "${qt_bin_dir}")
  else()
    find_program(
      WINDEPLOYQT_EXECUTABLE
      NAMES windeployqt
      HINTS "${qt_bin_dir}")
  endif()
  if(NOT EXISTS ${WINDEPLOYQT_EXECUTABLE})
    find_program(
      WINDEPLOYQT_EXECUTABLE
      NAMES windeployqt
      HINTS "${qt_bin_dir}")
  endif()
  if(NOT EXISTS ${WINDEPLOYQT_EXECUTABLE})
    find_program(
      WINDEPLOYQT_EXECUTABLE
      NAMES windeployqt.exe
      HINTS "${qt_bin_dir}")
  endif()
endif(WIN32)

# 用于deploy qt的相关依赖
function(deployqt TARGET_NAME)
  # use windeploy
  if(WIN32)
    # install system runtime lib
    include(InstallRequiredSystemLibraries)
    if(CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS)
      install(
        PROGRAMS ${CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS}
        DESTINATION ${CMAKE_INSTALL_PREFIX}
        COMPONENT System)

      # 在构建完成后拷贝依赖 DLL 文件
      add_custom_command(
        TARGET ${TARGET_NAME}
        POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS}
                $<TARGET_FILE_DIR:${TARGET_NAME}>)
    endif(CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS)

    get_filename_component(CMAKE_CXX_COMPILER_BINPATH ${CMAKE_CXX_COMPILER} DIRECTORY)
    add_custom_command(
      TARGET ${TARGET_NAME}
      POST_BUILD
      COMMAND
        "${CMAKE_COMMAND}" -E env PATH="${QT_DEPEND_LIBRARY}" "${WINDEPLOYQT_EXECUTABLE}"
        \"$<TARGET_FILE:${TARGET_NAME}>\" --no-compiler-runtime --no-angle --no-opengl-sw --no-quick-import
        --no-system-d3d-compiler --concurrent --verbose=1
      COMMENT "Running windeployqt ... ")
  endif(WIN32)
endfunction(deployqt)
