﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef RS_EXPECTED_H
#define RS_EXPECTED_H

#include "3rd_party/tl/expected.hpp"
#include <vector>

namespace robosense::lidar
{

// 提取 expected 的 error_type
template <typename T>
struct expected_error_type;

template <typename V, typename E>
struct expected_error_type<tl::expected<V, E>>
{
  using type = E;
};

// 自动推断 error_type
template <typename First, typename... Rest>
auto execAll(First&& _first, Rest&&... _rest)
{
  using ErrorType = typename expected_error_type<std::decay_t<First>>::type;

  static_assert((... && std::is_same_v<ErrorType, typename expected_error_type<std::decay_t<Rest>>::type>),
                "All expects must have the same error_type!");

  std::vector<ErrorType> errors;

  auto collect = [&](auto&& _exp) {
    if (!_exp)
    {
      errors.emplace_back(_exp.error());
    }
  };

  collect(std::forward<First>(_first));
  (collect(std::forward<Rest>(_rest)), ...);

  using ResultType = tl::expected<void, std::vector<ErrorType>>;

  if (!errors.empty())
  {
    return ResultType(tl::unexpect, errors);
  }
  return ResultType {};
}

// 支持容器的execAll函数
template <typename Container>
auto execAllContainer(const Container& _container)
{
  if (_container.empty())
  {
    // 如果容器为空，返回成功
    return tl::expected<void, std::vector<typename expected_error_type<typename Container::value_type>::type>> {};
  }

  using ExpectedType = typename Container::value_type;
  using ErrorType    = typename expected_error_type<ExpectedType>::type;
  std::vector<ErrorType> errors;

  // 检查所有操作的结果
  for (const auto& operation : _container)
  {
    if (!operation)
    {
      errors.emplace_back(operation.error());
    }
  }

  using ResultType = tl::expected<void, std::vector<ErrorType>>;

  if (!errors.empty())
  {
    return ResultType(tl::unexpect, errors);
  }
  return ResultType {};
}

// 辅助类，用于构建和执行操作链
template <typename ErrorType>
class Executor
{
public:
  using ExpectedType = tl::expected<void, ErrorType>;
  using ResultType   = tl::expected<void, std::vector<ErrorType>>;

  // 添加一个操作
  template <typename Func>
  Executor& addTask(Func&& _func)
  {
    operations_.emplace_back(std::forward<Func>(_func)());
    return *this;
  }

  // 条件添加一个操作
  template <typename Func>
  Executor& addTaskIf(bool _condition, Func&& _func)
  {
    if (_condition)
    {
      operations_.emplace_back(std::forward<Func>(_func)());
    }
    return *this;
  }

  // 执行所有操作并返回结果
  ResultType execute() { return execAllContainer(operations_); }

  // 获取所有操作的结果
  const std::vector<ExpectedType>& getOperations() const { return operations_; }

private:
  std::vector<ExpectedType> operations_;
};

template <typename T, typename E>
using Expected = tl::expected<T, E>;

template <typename E>
[[nodiscard]] constexpr auto makeError(E&& _err)
{
  return tl::make_unexpected(std::forward<E>(_err));
}

}  // namespace robosense::lidar

#endif
