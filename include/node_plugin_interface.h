﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef NODE_PLUGIN_INTERFACE_H
#define NODE_PLUGIN_INTERFACE_H

#include "common_types.h"
#include <QtPlugin>

namespace robosense::lidar
{

class NodeManager;

class NodePluginInterface
{
public:
  NodePluginInterface()          = default;
  virtual ~NodePluginInterface() = default;

  NodePluginInterface(const NodePluginInterface&) = delete;
  NodePluginInterface& operator=(const NodePluginInterface&) = delete;
  NodePluginInterface(NodePluginInterface&&)                 = delete;
  NodePluginInterface& operator=(NodePluginInterface&&) = delete;

  /**
   * @brief 获取插件名称
   * @return 插件名称
   */
  [[nodiscard]] virtual std::string name() const = 0;

  /**
   * @brief 获取插件版本
   * @return 插件版本
   */
  [[nodiscard]] virtual std::string version() const = 0;

  /**
   * @brief 初始化插件
   * @return 初始化是否成功
   */
  virtual bool initialize(NodeManager* _node_manager) = 0;

  /**
   * @brief 获取插件提供的自定义节点类型
   * @return 节点类型列表
   */
  [[nodiscard]] virtual NodeInfos nodeInfos() const = 0;
};

}  // namespace robosense::lidar

#define NodePluginInterface_iid "rs_node_plugin_interface"
Q_DECLARE_INTERFACE(robosense::lidar::NodePluginInterface, NodePluginInterface_iid)

#endif  // NODE_PLUGIN_INTERFACE_H